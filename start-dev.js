#!/usr/bin/env node

// Simple script to start Next.js development server
const { spawn } = require('child_process');
const path = require('path');

// Try to find and run Next.js
const nextPath = path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next');

console.log('Starting FreshSteam development server...');
console.log('Next.js path:', nextPath);

const child = spawn('node', [nextPath, 'dev'], {
  stdio: 'inherit',
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('Failed to start server:', error);
});

child.on('close', (code) => {
  console.log(`Server process exited with code ${code}`);
});
