'use client'

import { useEffect, useState } from 'react'
import Image from 'next/image'
import { useCart } from '@/store/cart'

// Simple icon component
const PlusIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
)

interface Side {
  id: string
  name: string
  description: string
  category: string
  price: number
  imageUrl: string
  isAvailable: boolean
}

export function FeaturedSides() {
  const [sides, setSides] = useState<Side[]>([])
  const [loading, setLoading] = useState(true)
  const { addItem } = useCart()

  useEffect(() => {
    async function fetchSides() {
      try {
        const response = await fetch('/api/products')
        if (!response.ok) {
          throw new Error('Failed to fetch sides')
        }
        const data = await response.json()
        setSides(data.sides || [])
      } catch (err) {
        console.error('Error fetching sides:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchSides()
  }, [])

  const handleAddToCart = (side: Side) => {
    addItem({
      id: side.id,
      type: 'side',
      name: side.name,
      price: side.price,
      imageUrl: side.imageUrl,
      category: side.category,
    })
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="card p-6 animate-pulse">
            <div className="bg-gray-300 h-32 rounded-lg mb-4"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded mb-4 w-3/4"></div>
            <div className="h-8 bg-gray-300 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  // Group sides by category
  const groupedSides = sides.reduce((acc, side) => {
    if (!acc[side.category]) {
      acc[side.category] = []
    }
    acc[side.category].push(side)
    return acc
  }, {} as Record<string, Side[]>)

  const categoryOrder = ['chutney', 'curry', 'vegetable']
  const categoryNames = {
    chutney: 'Traditional Chutneys',
    curry: 'Curries & Soups',
    vegetable: 'Vegetable Sides'
  }

  const categoryIcons = {
    chutney: '🥥',
    curry: '🍲',
    vegetable: '🥬'
  }

  return (
    <div className="space-y-12">
      {categoryOrder.map((category) => {
        const categorySides = groupedSides[category]
        if (!categorySides || categorySides.length === 0) return null

        return (
          <div key={category}>
            <div className="text-center mb-8">
              <div className="text-4xl mb-2">
                {categoryIcons[category as keyof typeof categoryIcons]}
              </div>
              <h3 className="text-2xl font-bold text-gray-900">
                {categoryNames[category as keyof typeof categoryNames]}
              </h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categorySides.map((side) => (
                <div key={side.id} className="card-hover overflow-hidden group">
                  {/* Side Image */}
                  <div className="relative h-32 bg-gray-200">
                    {side.imageUrl ? (
                      <Image
                        src={side.imageUrl}
                        alt={side.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400">No Image</span>
                      </div>
                    )}
                    
                    {!side.isAvailable && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium">
                        Out of Stock
                      </div>
                    )}
                  </div>

                  {/* Side Info */}
                  <div className="p-4">
                    <div className="mb-3">
                      <h4 className="text-lg font-semibold text-gray-900 mb-1">
                        {side.name}
                      </h4>
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {side.description}
                      </p>
                    </div>

                    {/* Price and Add to Cart */}
                    <div className="flex items-center justify-between">
                      <div className="text-lg font-bold text-gray-900">
                        ${side.price.toFixed(2)}
                      </div>
                      
                      <button
                        onClick={() => handleAddToCart(side)}
                        disabled={!side.isAvailable}
                        className={`
                          flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200
                          ${side.isAvailable
                            ? 'bg-secondary-500 hover:bg-secondary-600 text-white'
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          }
                        `}
                      >
                        <PlusIcon className="h-4 w-4" />
                        <span>Add</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )
      })}
    </div>
  )
}
