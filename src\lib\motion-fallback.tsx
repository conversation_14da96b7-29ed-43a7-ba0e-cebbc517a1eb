// Fallback components for framer-motion in case of dependency issues
import { ReactNode, HTMLAttributes } from 'react'

interface MotionProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode
  initial?: any
  animate?: any
  exit?: any
  transition?: any
  whileHover?: any
  whileTap?: any
  layout?: boolean
}

export const motion = {
  div: ({ children, className, onClick, ...props }: MotionProps) => (
    <div className={className} onClick={onClick} {...props}>
      {children}
    </div>
  ),
  button: ({ children, className, onClick, ...props }: MotionProps) => (
    <button className={className} onClick={onClick} {...props}>
      {children}
    </button>
  )
}

export const AnimatePresence = ({ children }: { children: ReactNode }) => (
  <>{children}</>
)
