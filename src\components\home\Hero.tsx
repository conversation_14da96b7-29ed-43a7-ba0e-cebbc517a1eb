import Link from 'next/link'
import Image from 'next/image'

export function Hero() {
  return (
    <section className="relative bg-gradient-to-r from-primary-50 to-secondary-50 overflow-hidden">
      <div className="container mx-auto px-4 py-16 lg:py-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Authentic
                <span className="text-primary-500 block">South Indian</span>
                Flavors
              </h1>
              <p className="text-xl text-gray-600 max-w-lg">
                Experience the rich traditions of South Indian cuisine with our freshly prepared 
                idlis, dosas, and aromatic rice dishes, served with authentic chutneys and curries.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/menu"
                className="btn-primary text-center px-8 py-4 text-lg font-semibold"
              >
                Order Now
              </Link>
              <Link
                href="/about"
                className="btn-outline text-center px-8 py-4 text-lg font-semibold"
              >
                Learn More
              </Link>
            </div>

            {/* Features */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center">
                <div className="text-2xl mb-2">🍽️</div>
                <p className="text-sm font-medium text-gray-700">Authentic Recipes</p>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">🚚</div>
                <p className="text-sm font-medium text-gray-700">Fresh Delivery</p>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">⭐</div>
                <p className="text-sm font-medium text-gray-700">Premium Quality</p>
              </div>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative">
            <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="https://images.unsplash.com/photo-1589301760014-d929f3979dbc?w=800&h=600&fit=crop"
                alt="Authentic South Indian food spread with idlis, dosas, and traditional sides"
                fill
                className="object-cover"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            </div>
            
            {/* Floating Cards */}
            <div className="absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-4 hidden lg:block">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium">Fresh & Hot</span>
              </div>
            </div>
            
            <div className="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-4 hidden lg:block">
              <div className="text-center">
                <div className="text-lg font-bold text-primary-500">4.9★</div>
                <div className="text-xs text-gray-600">Customer Rating</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Background Pattern */}
      <div className="absolute top-0 right-0 -z-10 opacity-10">
        <svg width="404" height="404" fill="none" viewBox="0 0 404 404">
          <defs>
            <pattern id="85737c0e-0916-41d7-917f-596dc7edfa27" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <rect x="0" y="0" width="4" height="4" className="text-primary-500" fill="currentColor" />
            </pattern>
          </defs>
          <rect width="404" height="404" fill="url(#85737c0e-0916-41d7-917f-596dc7edfa27)" />
        </svg>
      </div>
    </section>
  )
}
