// FreshSteam API Integration Service
// Centralized API calls for the frontend

const API_BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'

class ApiService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/api`
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body)
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || data.error || 'API request failed')
      }

      return data
    } catch (error) {
      console.error(`API Error (${endpoint}):`, error)
      throw error
    }
  }

  // Products API
  async getProducts(filters = {}) {
    const params = new URLSearchParams(filters)
    return this.request(`/products?${params}`)
  }

  async getProduct(id) {
    return this.request(`/products/${id}`)
  }

  async createProduct(productData) {
    return this.request('/products', {
      method: 'POST',
      body: productData
    })
  }

  async updateProduct(id, updates) {
    return this.request(`/products/${id}`, {
      method: 'PUT',
      body: updates
    })
  }

  async deleteProduct(id) {
    return this.request(`/products/${id}`, {
      method: 'DELETE'
    })
  }

  // Sides API
  async getSides(filters = {}) {
    const params = new URLSearchParams(filters)
    return this.request(`/sides?${params}`)
  }

  async createSide(sideData) {
    return this.request('/sides', {
      method: 'POST',
      body: sideData
    })
  }

  async updateSide(id, updates) {
    return this.request(`/sides/${id}`, {
      method: 'PUT',
      body: updates
    })
  }

  async deleteSide(id) {
    return this.request(`/sides/${id}`, {
      method: 'DELETE'
    })
  }

  // Traditional Combos API
  async getCombos(filters = {}) {
    const params = new URLSearchParams(filters)
    return this.request(`/combos?${params}`)
  }

  async addComboToCart(sessionId, comboId, customizations = {}) {
    return this.request('/combos', {
      method: 'POST',
      body: { sessionId, comboId, customizations }
    })
  }

  // Cart API
  async createCartSession() {
    return this.request('/cart', {
      method: 'POST',
      body: { action: 'create_session' }
    })
  }

  async getCart(sessionId) {
    return this.request(`/cart?sessionId=${sessionId}`)
  }

  async addToCart(sessionId, item) {
    return this.request('/cart', {
      method: 'POST',
      body: {
        action: 'add_item',
        sessionId,
        item
      }
    })
  }

  async updateCartItem(itemId, updates) {
    return this.request('/cart', {
      method: 'PUT',
      body: { itemId, ...updates }
    })
  }

  async removeFromCart(itemId) {
    return this.request(`/cart?itemId=${itemId}`, {
      method: 'DELETE'
    })
  }

  // Payment API
  async createPaymentIntent(orderData) {
    return this.request('/payment/create-intent', {
      method: 'POST',
      body: orderData
    })
  }

  async confirmPayment(paymentIntentId, orderId) {
    return this.request('/payment/confirm', {
      method: 'POST',
      body: { paymentIntentId, orderId }
    })
  }

  // Orders API
  async getOrder(orderId) {
    return this.request(`/orders/${orderId}`)
  }

  async getOrderByNumber(orderNumber) {
    return this.request(`/orders/number/${orderNumber}`)
  }

  async updateOrderStatus(orderId, status) {
    return this.request(`/orders/${orderId}/status`, {
      method: 'PUT',
      body: { status }
    })
  }

  // Admin API
  async adminLogin(credentials) {
    return this.request('/admin/login', {
      method: 'POST',
      body: credentials
    })
  }

  async getAdminDashboard() {
    return this.request('/admin/dashboard')
  }

  async getOrdersAdmin(filters = {}) {
    const params = new URLSearchParams(filters)
    return this.request(`/admin/orders?${params}`)
  }

  async updateInventory(itemId, itemType, updates) {
    return this.request(`/admin/inventory/${itemType}/${itemId}`, {
      method: 'PUT',
      body: updates
    })
  }
}

// Create singleton instance
const api = new ApiService()

// Export individual methods for convenience
export const {
  // Products
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  
  // Sides
  getSides,
  createSide,
  updateSide,
  deleteSide,
  
  // Combos
  getCombos,
  addComboToCart,
  
  // Cart
  createCartSession,
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  
  // Payment
  createPaymentIntent,
  confirmPayment,
  
  // Orders
  getOrder,
  getOrderByNumber,
  updateOrderStatus,
  
  // Admin
  adminLogin,
  getAdminDashboard,
  getOrdersAdmin,
  updateInventory
} = api

export default api
