-- FreshSteam Seed Data
-- Authentic South Indian Food Menu with Cultural Context

-- Insert Categories
INSERT INTO categories (name, description, display_order) VALUES
('Idli', 'Soft, fluffy steamed rice cakes - the heart of South Indian breakfast', 1),
('Dosa', 'Crispy golden crepes made from fermented rice and lentil batter', 2),
('Rice Dishes', 'Traditional rice preparations with aromatic spices and accompaniments', 3),
('Vada', 'Deep-fried savory donuts made from lentil batter', 4),
('Uttapam', 'Thick pancakes topped with fresh vegetables', 5);

-- Insert Products (Main Dishes)
INSERT INTO products (category_id, name, description, cultural_context, base_price, image_url, ingredients, allergens, dietary_info, spice_level, prep_time, is_featured, display_order) VALUES
-- Idli Category
((SELECT id FROM categories WHERE name = 'Idli'), 'Plain Idli (4 pieces)', 'Soft, fluffy steamed rice cakes made from fermented rice and lentil batter. A classic South Indian breakfast staple that represents the essence of simple, healthy eating.', '<PERSON><PERSON><PERSON> is considered the king of South Indian breakfast. Traditionally served during festivals and auspicious occasions, it symbolizes purity and simplicity in Tamil culture.', 8.99, 'https://images.unsplash.com/photo-1589301760014-d929f3979dbc?w=400&h=300&fit=crop', ARRAY['Rice', 'Black Gram Dal', 'Fenugreek Seeds', 'Salt'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 1, 15, true, 1),

((SELECT id FROM categories WHERE name = 'Idli'), 'Rava Idli (4 pieces)', 'Instant semolina idlis with a unique texture, garnished with curry leaves, mustard seeds, and cashews. A modern twist on the traditional recipe.', 'Rava Idli was invented in Karnataka and became popular across South India. It represents the innovation in traditional cooking while maintaining cultural authenticity.', 9.99, 'https://images.unsplash.com/photo-1589301760014-d929f3979dbc?w=400&h=300&fit=crop', ARRAY['Semolina', 'Yogurt', 'Curry Leaves', 'Mustard Seeds', 'Cashews', 'Ginger'], ARRAY['Nuts'], '{"vegetarian": true, "vegan": false, "gluten_free": false}', 1, 10, false, 2),

-- Dosa Category
((SELECT id FROM categories WHERE name = 'Dosa'), 'Plain Dosa', 'Crispy golden crepe made from fermented rice and lentil batter. The foundation of South Indian cuisine, perfect in its simplicity.', 'Dosa originated in Karnataka and Tamil Nadu over 1000 years ago. It represents the art of fermentation in Indian cooking and is considered a complete protein when served with sambar.', 10.99, 'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?w=400&h=300&fit=crop', ARRAY['Rice', 'Black Gram Dal', 'Fenugreek Seeds', 'Salt'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 1, 12, true, 1),

((SELECT id FROM categories WHERE name = 'Dosa'), 'Masala Dosa', 'Crispy golden crepe filled with spiced potato curry, served with traditional accompaniments. The most beloved dosa variety.', 'Masala Dosa is the crown jewel of South Indian cuisine. The potato filling (aloo palya) represents the perfect balance of spices and comfort, making it a favorite across India.', 12.99, 'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?w=400&h=300&fit=crop', ARRAY['Rice', 'Black Gram Dal', 'Potatoes', 'Onions', 'Green Chilies', 'Turmeric', 'Mustard Seeds', 'Curry Leaves'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 2, 15, true, 2),

((SELECT id FROM categories WHERE name = 'Dosa'), 'Mysore Masala Dosa', 'Spicy dosa with red chutney spread, filled with potato masala. A fiery delight from the royal city of Mysore.', 'Mysore Masala Dosa originated in the royal kitchens of Mysore Palace. The red chutney represents the bold flavors preferred by the Wodeyar dynasty.', 13.99, 'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?w=400&h=300&fit=crop', ARRAY['Rice', 'Black Gram Dal', 'Potatoes', 'Red Chilies', 'Garlic', 'Tamarind', 'Jaggery'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 4, 18, false, 3),

-- Rice Dishes Category
((SELECT id FROM categories WHERE name = 'Rice Dishes'), 'Sambar Rice', 'Aromatic rice mixed with traditional sambar, a complete meal representing the essence of South Indian home cooking.', 'Sambar Rice is the ultimate comfort food in South Indian households. It represents the concept of "annam brahma" - rice as the divine sustenance of life.', 11.99, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', ARRAY['Rice', 'Toor Dal', 'Tamarind', 'Drumsticks', 'Okra', 'Tomatoes', 'Sambar Powder'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 3, 20, true, 1),

((SELECT id FROM categories WHERE name = 'Rice Dishes'), 'Curd Rice', 'Cooling rice mixed with fresh yogurt, tempered with mustard seeds, curry leaves, and ginger. Perfect for hot days.', 'Curd Rice (Thayir Sadam) is considered sacred in Tamil culture. It is always served at the end of meals and during religious ceremonies for its cooling and digestive properties.', 9.99, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', ARRAY['Rice', 'Yogurt', 'Mustard Seeds', 'Curry Leaves', 'Ginger', 'Green Chilies'], ARRAY[], '{"vegetarian": true, "vegan": false, "gluten_free": true}', 1, 10, false, 2);

-- Insert Sides (Accompaniments)
INSERT INTO sides (name, description, cultural_context, price, image_url, category, ingredients, allergens, dietary_info, spice_level, display_order) VALUES
('Coconut Chutney', 'Fresh coconut ground with green chilies, ginger, and curry leaves. The perfect cooling accompaniment to spicy South Indian dishes.', 'Coconut chutney represents abundance and prosperity in South Indian culture. Coconut is considered sacred and is offered to deities in temples.', 3.99, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', 'chutney', ARRAY['Fresh Coconut', 'Green Chilies', 'Ginger', 'Curry Leaves', 'Salt'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 2, 1),

('Sambar', 'Traditional lentil soup with vegetables, tamarind, and aromatic spices. The soul of South Indian cuisine.', 'Sambar is believed to have originated in the Maratha court of Thanjavur. It represents the perfect balance of taste, nutrition, and tradition in South Indian cooking.', 4.99, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', 'curry', ARRAY['Toor Dal', 'Tamarind', 'Drumsticks', 'Okra', 'Tomatoes', 'Onions', 'Sambar Powder'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 3, 2),

('Tomato Chutney', 'Tangy and spicy chutney made with ripe tomatoes, red chilies, and traditional tempering.', 'Tomato chutney became popular in South India after tomatoes were introduced. It represents the adaptability of traditional cooking to new ingredients.', 3.99, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', 'chutney', ARRAY['Tomatoes', 'Red Chilies', 'Garlic', 'Tamarind', 'Jaggery', 'Mustard Seeds'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 4, 3),

('Mint Chutney', 'Refreshing chutney made with fresh mint leaves, coriander, and green chilies. A cooling complement to any meal.', 'Mint chutney is prized for its digestive properties in Ayurveda. It represents the healing aspect of South Indian cuisine.', 3.99, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', 'chutney', ARRAY['Mint Leaves', 'Coriander Leaves', 'Green Chilies', 'Ginger', 'Lemon Juice'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 2, 4),

('Rasam', 'Tangy tamarind soup with tomatoes, spices, and curry leaves. A digestive and comforting South Indian staple.', 'Rasam is considered medicine in South Indian households. It aids digestion and is often the first food given to someone recovering from illness.', 4.99, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', 'curry', ARRAY['Tamarind', 'Tomatoes', 'Rasam Powder', 'Curry Leaves', 'Coriander Leaves', 'Ghee'], ARRAY[], '{"vegetarian": true, "vegan": false, "gluten_free": true}', 3, 5),

('Papad (2 pieces)', 'Crispy lentil wafers, roasted to perfection. A traditional accompaniment that adds crunch to every meal.', 'Papad making is a traditional art passed down through generations of South Indian women. It represents patience, skill, and family traditions.', 2.99, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', 'accompaniment', ARRAY['Black Gram Dal', 'Salt', 'Cumin Seeds', 'Black Pepper'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 1, 6),

('Pickle (Mixed Vegetable)', 'Traditional South Indian pickle with mixed vegetables, oil, and spices. A burst of flavor in every bite.', 'Pickle (Oorugai) is an essential part of South Indian meals. It represents preservation techniques and the art of balancing flavors for long-term storage.', 3.49, 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop', 'pickle', ARRAY['Mixed Vegetables', 'Mustard Oil', 'Red Chili Powder', 'Turmeric', 'Fenugreek Powder', 'Salt'], ARRAY[], '{"vegetarian": true, "vegan": true, "gluten_free": true}', 5, 7);

-- Insert Traditional Combos
INSERT INTO traditional_combos (name, description, cultural_significance, discount_percentage, display_order) VALUES
('Classic South Indian Breakfast', 'The perfect traditional breakfast: Idli with Sambar and Coconut Chutney', 'This combination represents the ideal balance of carbohydrates, proteins, and probiotics. It has been the foundation of South Indian breakfast for over 1000 years, providing sustained energy and digestive health.', 5.00, 1),

('Dosa Feast', 'Complete dosa experience: Masala Dosa with Sambar, Coconut Chutney, and Tomato Chutney', 'This combo showcases the versatility of dosa and the harmony of different chutneys. It represents the art of balancing flavors - sweet, sour, spicy, and savory in one meal.', 7.00, 2),

('Comfort Rice Meal', 'Homestyle comfort: Sambar Rice with Rasam, Papad, and Pickle', 'This combination replicates a traditional South Indian home meal. It represents the concept of "complete nutrition" with rice as the base, protein from dal, and digestive aids from rasam and pickle.', 8.00, 3),

('Festival Special', 'Celebration platter: Rava Idli with Sambar, Coconut Chutney, Mint Chutney, and Papad', 'This elaborate combination is served during festivals and special occasions. It represents abundance, celebration, and the joy of sharing food with loved ones in South Indian culture.', 10.00, 4);

-- Insert Traditional Combo Items
-- Classic South Indian Breakfast: Idli + Sambar + Coconut Chutney
INSERT INTO traditional_combo_items (combo_id, product_id, side_id, quantity) VALUES
((SELECT id FROM traditional_combos WHERE name = 'Classic South Indian Breakfast'), (SELECT id FROM products WHERE name = 'Plain Idli (4 pieces)'), NULL, 1),
((SELECT id FROM traditional_combos WHERE name = 'Classic South Indian Breakfast'), NULL, (SELECT id FROM sides WHERE name = 'Sambar'), 1),
((SELECT id FROM traditional_combos WHERE name = 'Classic South Indian Breakfast'), NULL, (SELECT id FROM sides WHERE name = 'Coconut Chutney'), 1);

-- Dosa Feast: Masala Dosa + Sambar + Coconut Chutney + Tomato Chutney
INSERT INTO traditional_combo_items (combo_id, product_id, side_id, quantity) VALUES
((SELECT id FROM traditional_combos WHERE name = 'Dosa Feast'), (SELECT id FROM products WHERE name = 'Masala Dosa'), NULL, 1),
((SELECT id FROM traditional_combos WHERE name = 'Dosa Feast'), NULL, (SELECT id FROM sides WHERE name = 'Sambar'), 1),
((SELECT id FROM traditional_combos WHERE name = 'Dosa Feast'), NULL, (SELECT id FROM sides WHERE name = 'Coconut Chutney'), 1),
((SELECT id FROM traditional_combos WHERE name = 'Dosa Feast'), NULL, (SELECT id FROM sides WHERE name = 'Tomato Chutney'), 1);

-- Comfort Rice Meal: Sambar Rice + Rasam + Papad + Pickle
INSERT INTO traditional_combo_items (combo_id, product_id, side_id, quantity) VALUES
((SELECT id FROM traditional_combos WHERE name = 'Comfort Rice Meal'), (SELECT id FROM products WHERE name = 'Sambar Rice'), NULL, 1),
((SELECT id FROM traditional_combos WHERE name = 'Comfort Rice Meal'), NULL, (SELECT id FROM sides WHERE name = 'Rasam'), 1),
((SELECT id FROM traditional_combos WHERE name = 'Comfort Rice Meal'), NULL, (SELECT id FROM sides WHERE name = 'Papad (2 pieces)'), 1),
((SELECT id FROM traditional_combos WHERE name = 'Comfort Rice Meal'), NULL, (SELECT id FROM sides WHERE name = 'Pickle (Mixed Vegetable)'), 1);

-- Festival Special: Rava Idli + Sambar + Coconut Chutney + Mint Chutney + Papad
INSERT INTO traditional_combo_items (combo_id, product_id, side_id, quantity) VALUES
((SELECT id FROM traditional_combos WHERE name = 'Festival Special'), (SELECT id FROM products WHERE name = 'Rava Idli (4 pieces)'), NULL, 1),
((SELECT id FROM traditional_combos WHERE name = 'Festival Special'), NULL, (SELECT id FROM sides WHERE name = 'Sambar'), 1),
((SELECT id FROM traditional_combos WHERE name = 'Festival Special'), NULL, (SELECT id FROM sides WHERE name = 'Coconut Chutney'), 1),
((SELECT id FROM traditional_combos WHERE name = 'Festival Special'), NULL, (SELECT id FROM sides WHERE name = 'Mint Chutney'), 1),
((SELECT id FROM traditional_combos WHERE name = 'Festival Special'), NULL, (SELECT id FROM sides WHERE name = 'Papad (2 pieces)'), 1);

-- Update combo total prices based on individual item prices
UPDATE traditional_combos SET total_price = (
    SELECT ROUND(
        (COALESCE(SUM(p.base_price), 0) + COALESCE(SUM(s.price), 0)) * (1 - discount_percentage/100), 2
    )
    FROM traditional_combo_items tci
    LEFT JOIN products p ON tci.product_id = p.id
    LEFT JOIN sides s ON tci.side_id = s.id
    WHERE tci.combo_id = traditional_combos.id
);

-- Create default admin user (password: admin123 - should be changed in production)
INSERT INTO admin_users (email, password_hash, name, role) VALUES
('<EMAIL>', '$2b$10$rQZ8kHWfQYwjQYwjQYwjQOeKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK', 'FreshSteam Admin', 'admin');
