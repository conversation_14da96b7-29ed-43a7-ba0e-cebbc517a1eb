'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { SparklesIcon, HeartIcon } from '@heroicons/react/24/outline'

interface Product {
  id: string
  name: string
  description: string
  category: string
  variety: string
  basePrice: number
  imageUrl: string
  isAvailable: boolean
}

interface Side {
  id: string
  name: string
  description: string
  category: string
  price: number
  imageUrl: string
  isAvailable: boolean
}

interface TraditionalCombosProps {
  products: Product[]
  sides: Side[]
  onSelectCombo: (main: Product, sides: Side[]) => void
}

interface ComboDefinition {
  id: string
  name: string
  description: string
  cultural_significance: string
  mainCategory: string
  sideNames: string[]
  icon: string
  popularity: 'high' | 'medium' | 'low'
}

export function TraditionalCombos({ products, sides, onSelectCombo }: TraditionalCombosProps) {
  // Define traditional South Indian meal combinations
  const traditionalCombos: ComboDefinition[] = [
    {
      id: 'classic-idli-combo',
      name: 'Classic Idli Breakfast',
      description: 'The most beloved South Indian breakfast combination',
      cultural_significance: 'Traditional morning meal served in every South Indian household',
      mainCategory: 'idli',
      sideNames: ['Coconut Chutney', 'Sambar'],
      icon: '🌅',
      popularity: 'high'
    },
    {
      id: 'dosa-feast',
      name: 'Dosa Feast',
      description: 'Complete dosa experience with variety of accompaniments',
      cultural_significance: 'Popular weekend brunch enjoyed across South India',
      mainCategory: 'dosa',
      sideNames: ['Coconut Chutney', 'Tomato Chutney', 'Sambar'],
      icon: '🎉',
      popularity: 'high'
    },
    {
      id: 'comfort-rice-meal',
      name: 'Comfort Rice Meal',
      description: 'Hearty rice meal with traditional curries',
      cultural_significance: 'Everyday lunch meal that provides complete nutrition',
      mainCategory: 'rice',
      sideNames: ['Rasam', 'Mixed Vegetable Curry'],
      icon: '🏠',
      popularity: 'medium'
    },
    {
      id: 'festival-special',
      name: 'Festival Special',
      description: 'Elaborate meal for special occasions',
      cultural_significance: 'Served during festivals and celebrations',
      mainCategory: 'idli',
      sideNames: ['Coconut Chutney', 'Peanut Chutney', 'Sambar', 'Mixed Vegetable Curry'],
      icon: '🎊',
      popularity: 'medium'
    }
  ]

  const getComboItems = (combo: ComboDefinition) => {
    // Find the main product
    const mainProduct = products.find(p => 
      p.category === combo.mainCategory && p.isAvailable
    )
    
    // Find the side items
    const comboSides = sides.filter(s => 
      combo.sideNames.includes(s.name) && s.isAvailable
    )
    
    return { mainProduct, comboSides }
  }

  const calculateComboPrice = (combo: ComboDefinition) => {
    const { mainProduct, comboSides } = getComboItems(combo)
    
    if (!mainProduct) return 0
    
    const mainPrice = mainProduct.basePrice
    const sidesPrice = comboSides.reduce((total, side) => total + side.price, 0)
    const subtotal = mainPrice + sidesPrice
    
    // Apply 5% combo discount
    return subtotal * 0.95
  }

  const isComboAvailable = (combo: ComboDefinition) => {
    const { mainProduct, comboSides } = getComboItems(combo)
    return mainProduct && comboSides.length >= 2
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          🍽️ Traditional Meal Combinations
        </h3>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Experience authentic South Indian dining with these time-honored meal combinations, 
          each with cultural significance and perfect flavor balance.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {traditionalCombos.map((combo, index) => {
          const { mainProduct, comboSides } = getComboItems(combo)
          const isAvailable = isComboAvailable(combo)
          const comboPrice = calculateComboPrice(combo)
          const originalPrice = mainProduct ? 
            mainProduct.basePrice + comboSides.reduce((total, side) => total + side.price, 0) : 0
          const savings = originalPrice - comboPrice

          return (
            <motion.div
              key={combo.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`
                card-hover transition-all duration-300 overflow-hidden
                ${isAvailable 
                  ? 'cursor-pointer hover:shadow-xl' 
                  : 'opacity-50 cursor-not-allowed'
                }
                ${combo.popularity === 'high' ? 'ring-2 ring-primary-200' : ''}
              `}
              onClick={() => {
                if (isAvailable && mainProduct) {
                  onSelectCombo(mainProduct, comboSides)
                }
              }}
            >
              {/* Combo Header */}
              <div className="relative p-6 bg-gradient-to-br from-primary-50 to-secondary-50">
                {combo.popularity === 'high' && (
                  <div className="absolute top-3 right-3 bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
                    <HeartIcon className="h-3 w-3" />
                    <span>Popular</span>
                  </div>
                )}
                
                <div className="flex items-start space-x-4">
                  <div className="text-4xl">{combo.icon}</div>
                  <div className="flex-1">
                    <h4 className="text-lg font-bold text-gray-900 mb-1">
                      {combo.name}
                    </h4>
                    <p className="text-sm text-gray-600 mb-2">
                      {combo.description}
                    </p>
                    <p className="text-xs text-primary-600 italic">
                      {combo.cultural_significance}
                    </p>
                  </div>
                </div>
              </div>

              {/* Combo Items */}
              <div className="p-6 space-y-4">
                {/* Main Item */}
                {mainProduct && (
                  <div className="flex items-center space-x-3 p-3 bg-primary-50 rounded-lg">
                    <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                      {mainProduct.imageUrl ? (
                        <Image
                          src={mainProduct.imageUrl}
                          alt={mainProduct.name}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No Image</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="font-semibold text-gray-900 text-sm">
                        {mainProduct.name}
                      </p>
                      <p className="text-xs text-gray-600">Main Dish</p>
                    </div>
                    <p className="text-sm font-medium text-gray-700">
                      ${mainProduct.basePrice.toFixed(2)}
                    </p>
                  </div>
                )}

                {/* Side Items */}
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">Includes:</p>
                  {comboSides.map((side) => (
                    <div key={side.id} className="flex items-center space-x-3 p-2 bg-secondary-50 rounded-lg">
                      <div className="w-8 h-8 bg-gray-200 rounded overflow-hidden">
                        {side.imageUrl ? (
                          <Image
                            src={side.imageUrl}
                            alt={side.name}
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-200"></div>
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 text-sm">{side.name}</p>
                      </div>
                      <p className="text-xs text-gray-600">${side.price.toFixed(2)}</p>
                    </div>
                  ))}
                </div>

                {/* Pricing */}
                {isAvailable && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 line-through">
                        Regular: ${originalPrice.toFixed(2)}
                      </span>
                      <div className="flex items-center space-x-1 text-green-600">
                        <SparklesIcon className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          Save ${savings.toFixed(2)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold text-gray-900">
                        Combo Price: ${comboPrice.toFixed(2)}
                      </span>
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        5% OFF
                      </span>
                    </div>
                  </div>
                )}

                {!isAvailable && (
                  <div className="bg-gray-100 rounded-lg p-4 text-center">
                    <p className="text-gray-500 text-sm">
                      Some items in this combo are currently unavailable
                    </p>
                  </div>
                )}
              </div>

              {/* Action Button */}
              {isAvailable && (
                <div className="px-6 pb-6">
                  <button className="w-full bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 rounded-lg transition-colors duration-200">
                    Select This Combo
                  </button>
                </div>
              )}
            </motion.div>
          )
        })}
      </div>

      {/* Cultural Information */}
      <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-6">
        <h4 className="font-bold text-orange-800 mb-3 flex items-center space-x-2">
          <span>🏛️</span>
          <span>Cultural Heritage</span>
        </h4>
        <div className="text-sm text-orange-700 space-y-2">
          <p>
            These traditional combinations have been perfected over centuries in South Indian households. 
            Each pairing is designed to provide balanced nutrition, complementary flavors, and optimal digestion.
          </p>
          <p>
            <strong>Did you know?</strong> The combination of rice/idli with sambar provides complete proteins, 
            while chutneys add essential vitamins and aid in digestion through natural enzymes.
          </p>
        </div>
      </div>
    </div>
  )
}
