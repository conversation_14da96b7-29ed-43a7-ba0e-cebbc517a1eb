'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { PlusIcon, MinusIcon, TrashIcon } from '@heroicons/react/24/outline'

interface MealBuilderItem {
  id: string
  type: 'product' | 'side'
  name: string
  price: number
  quantity: number
  imageUrl?: string
  category?: string
  variety?: string
}

interface MealSummaryProps {
  selectedMain: MealBuilderItem | null
  selectedSides: MealBuilderItem[]
  onUpdateMainQuantity: (quantity: number) => void
  onUpdateSideQuantity: (sideId: string, quantity: number) => void
  onRemoveSide: (sideId: string) => void
}

export function MealSummary({
  selectedMain,
  selectedSides,
  onUpdateMainQuantity,
  onUpdateSideQuantity,
  onRemoveSide
}: MealSummaryProps) {
  const hasItems = selectedMain || selectedSides.length > 0

  if (!hasItems) {
    return (
      <div className="bg-gray-50 rounded-lg p-6 text-center">
        <div className="text-4xl mb-3">🍽️</div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">
          Your Meal Summary
        </h3>
        <p className="text-gray-500">
          Select a main dish and sides to see your meal summary here
        </p>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white border border-gray-200 rounded-lg p-6 space-y-6"
    >
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          🍽️ Your Meal Summary
        </h3>
        <p className="text-sm text-gray-600">
          Review and adjust quantities before adding to cart
        </p>
      </div>

      {/* Main Item */}
      {selectedMain && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-800 border-b border-gray-200 pb-2">
            Main Dish
          </h4>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-3 p-3 bg-primary-50 rounded-lg border border-primary-200"
          >
            {/* Image */}
            <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
              {selectedMain.imageUrl ? (
                <Image
                  src={selectedMain.imageUrl}
                  alt={selectedMain.name}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-xs">No Image</span>
                </div>
              )}
            </div>

            {/* Details */}
            <div className="flex-1 min-w-0">
              <h5 className="font-semibold text-gray-900 truncate">
                {selectedMain.name}
              </h5>
              {selectedMain.variety && (
                <p className="text-xs text-primary-600 capitalize">
                  {selectedMain.variety} variety
                </p>
              )}
              <p className="text-sm text-gray-600">
                ${selectedMain.price.toFixed(2)} each
              </p>
            </div>

            {/* Quantity Controls */}
            <div className="flex items-center space-x-2 bg-white rounded-lg px-3 py-2 border">
              <button
                onClick={() => onUpdateMainQuantity(Math.max(1, selectedMain.quantity - 1))}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
              >
                <MinusIcon className="h-4 w-4 text-gray-600" />
              </button>
              <span className="font-medium text-gray-900 min-w-[24px] text-center">
                {selectedMain.quantity}
              </span>
              <button
                onClick={() => onUpdateMainQuantity(selectedMain.quantity + 1)}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
              >
                <PlusIcon className="h-4 w-4 text-gray-600" />
              </button>
            </div>

            {/* Subtotal */}
            <div className="text-right">
              <p className="font-bold text-gray-900">
                ${(selectedMain.price * selectedMain.quantity).toFixed(2)}
              </p>
            </div>
          </motion.div>
        </div>
      )}

      {/* Side Items */}
      {selectedSides.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-800 border-b border-gray-200 pb-2">
            Sides ({selectedSides.length})
          </h4>
          <div className="space-y-2">
            {selectedSides.map((side, index) => (
              <motion.div
                key={side.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center space-x-3 p-3 bg-secondary-50 rounded-lg border border-secondary-200"
              >
                {/* Image */}
                <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                  {side.imageUrl ? (
                    <Image
                      src={side.imageUrl}
                      alt={side.name}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-400 text-xs">No Image</span>
                    </div>
                  )}
                </div>

                {/* Details */}
                <div className="flex-1 min-w-0">
                  <h6 className="font-medium text-gray-900 text-sm truncate">
                    {side.name}
                  </h6>
                  <p className="text-xs text-gray-600">
                    ${side.price.toFixed(2)} each
                  </p>
                  {side.category && (
                    <p className="text-xs text-secondary-600 capitalize">
                      {side.category}
                    </p>
                  )}
                </div>

                {/* Quantity Controls */}
                <div className="flex items-center space-x-1 bg-white rounded-lg px-2 py-1 border">
                  <button
                    onClick={() => onUpdateSideQuantity(side.id, Math.max(0, side.quantity - 1))}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                  >
                    <MinusIcon className="h-3 w-3 text-gray-600" />
                  </button>
                  <span className="font-medium text-gray-900 text-sm min-w-[20px] text-center">
                    {side.quantity}
                  </span>
                  <button
                    onClick={() => onUpdateSideQuantity(side.id, side.quantity + 1)}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                  >
                    <PlusIcon className="h-3 w-3 text-gray-600" />
                  </button>
                </div>

                {/* Remove Button */}
                <button
                  onClick={() => onRemoveSide(side.id)}
                  className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                  title="Remove side"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>

                {/* Subtotal */}
                <div className="text-right min-w-[60px]">
                  <p className="font-medium text-gray-900 text-sm">
                    ${(side.price * side.quantity).toFixed(2)}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Meal Composition Summary */}
      <div className="bg-gray-50 rounded-lg p-4 space-y-2">
        <h4 className="font-medium text-gray-800 text-sm">Meal Composition</h4>
        <div className="text-sm text-gray-600 space-y-1">
          {selectedMain && (
            <p>• {selectedMain.quantity}x {selectedMain.name}</p>
          )}
          {selectedSides.map((side) => (
            <p key={side.id}>• {side.quantity}x {side.name}</p>
          ))}
        </div>
        
        {selectedMain && selectedSides.length > 0 && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              💡 This combination follows traditional South Indian meal patterns
            </p>
          </div>
        )}
      </div>
    </motion.div>
  )
}
