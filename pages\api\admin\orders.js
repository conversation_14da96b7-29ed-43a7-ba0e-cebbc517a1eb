// Admin orders management endpoint
import jwt from 'jsonwebtoken'
import { supabase } from '../../../lib/supabase'

// Middleware to verify admin token
function verifyAdminToken(req) {
  const authHeader = req.headers.authorization
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('No valid authorization token provided')
  }

  const token = authHeader.substring(7)
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret')
    return decoded
  } catch (error) {
    throw new Error('Invalid or expired token')
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  try {
    // Verify admin authentication
    const adminData = verifyAdminToken(req)

    switch (req.method) {
      case 'GET':
        await handleGetOrders(req, res)
        break
      case 'PUT':
        await handleUpdateOrder(req, res)
        break
      default:
        res.setHeader('Allow', ['GET', 'PUT'])
        res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('Admin orders API error:', error)
    
    if (error.message.includes('token') || error.message.includes('authorization')) {
      res.status(401).json({
        error: 'Unauthorized',
        message: error.message
      })
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      })
    }
  }
}

async function handleGetOrders(req, res) {
  const { 
    status, 
    payment_status, 
    page = 1, 
    limit = 20, 
    search,
    date_from,
    date_to 
  } = req.query

  try {
    let query = supabase
      .from('orders')
      .select(`
        id,
        order_number,
        customer_email,
        customer_phone,
        delivery_address,
        subtotal,
        tax_amount,
        delivery_fee,
        total_amount,
        payment_status,
        status,
        estimated_delivery,
        special_instructions,
        created_at,
        updated_at,
        order_items:order_items(
          id,
          product_name,
          side_name,
          quantity,
          unit_price,
          total_price,
          customizations
        )
      `)

    // Apply filters
    if (status) {
      query = query.eq('status', status)
    }
    
    if (payment_status) {
      query = query.eq('payment_status', payment_status)
    }
    
    if (search) {
      query = query.or(`order_number.ilike.%${search}%,customer_email.ilike.%${search}%`)
    }
    
    if (date_from) {
      query = query.gte('created_at', date_from)
    }
    
    if (date_to) {
      query = query.lte('created_at', date_to)
    }

    // Apply pagination
    const offset = (parseInt(page) - 1) * parseInt(limit)
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + parseInt(limit) - 1)

    const { data: orders, error, count } = await query

    if (error) throw error

    // Transform orders data
    const transformedOrders = orders.map(order => ({
      id: order.id,
      orderNumber: order.order_number,
      customer: {
        email: order.customer_email,
        phone: order.customer_phone
      },
      deliveryAddress: order.delivery_address,
      pricing: {
        subtotal: parseFloat(order.subtotal),
        taxAmount: parseFloat(order.tax_amount),
        deliveryFee: parseFloat(order.delivery_fee),
        totalAmount: parseFloat(order.total_amount)
      },
      status: order.status,
      paymentStatus: order.payment_status,
      estimatedDelivery: order.estimated_delivery,
      specialInstructions: order.special_instructions,
      items: order.order_items.map(item => ({
        id: item.id,
        name: item.product_name || item.side_name,
        type: item.product_name ? 'product' : 'side',
        quantity: item.quantity,
        unitPrice: parseFloat(item.unit_price),
        totalPrice: parseFloat(item.total_price),
        customizations: item.customizations || {}
      })),
      itemCount: order.order_items.length,
      totalQuantity: order.order_items.reduce((sum, item) => sum + item.quantity, 0),
      createdAt: order.created_at,
      updatedAt: order.updated_at
    }))

    res.status(200).json({
      success: true,
      data: {
        orders: transformedOrders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / parseInt(limit))
        }
      }
    })

  } catch (error) {
    throw new Error(`Failed to fetch orders: ${error.message}`)
  }
}

async function handleUpdateOrder(req, res) {
  const { orderId, status, estimatedDelivery, notes } = req.body

  if (!orderId || !status) {
    return res.status(400).json({
      error: 'Order ID and status are required'
    })
  }

  // Validate status
  const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled']
  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      error: 'Invalid status',
      validStatuses
    })
  }

  try {
    const updates = {
      status,
      updated_at: new Date().toISOString()
    }

    if (estimatedDelivery) {
      updates.estimated_delivery = estimatedDelivery
    }

    if (status === 'delivered') {
      updates.actual_delivery = new Date().toISOString()
    }

    const { data: updatedOrder, error } = await supabase
      .from('orders')
      .update(updates)
      .eq('id', orderId)
      .select(`
        id,
        order_number,
        status,
        payment_status,
        estimated_delivery,
        actual_delivery,
        updated_at
      `)
      .single()

    if (error) throw error

    res.status(200).json({
      success: true,
      data: updatedOrder,
      message: `Order ${updatedOrder.order_number} status updated to ${status}`
    })

  } catch (error) {
    throw new Error(`Failed to update order: ${error.message}`)
  }
}
