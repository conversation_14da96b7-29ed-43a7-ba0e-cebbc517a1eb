'use client'

import { useEffect, useState } from 'react'
import { ProductCard } from './ProductCard'

interface Product {
  id: string
  name: string
  description: string
  category: string
  variety: string
  basePrice: number
  imageUrl: string
  isAvailable: boolean
}

export function ProductGrid() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProducts() {
      try {
        const response = await fetch('/api/products')
        if (!response.ok) {
          throw new Error('Failed to fetch products')
        }
        const data = await response.json()
        setProducts(data.products || [])
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load products')
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="card p-6 animate-pulse">
            <div className="bg-gray-300 h-48 rounded-lg mb-4"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded mb-4 w-3/4"></div>
            <div className="h-8 bg-gray-300 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="btn-primary"
        >
          Try Again
        </button>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 mb-4">No products available at the moment.</p>
        <p className="text-sm text-gray-400">Please check back later.</p>
      </div>
    )
  }

  // Group products by category for better organization
  const groupedProducts = products.reduce((acc, product) => {
    if (!acc[product.category]) {
      acc[product.category] = []
    }
    acc[product.category].push(product)
    return acc
  }, {} as Record<string, Product[]>)

  const categoryOrder = ['idli', 'dosa', 'rice']
  const categoryNames = {
    idli: 'Idli Varieties',
    dosa: 'Dosa',
    rice: 'Rice Dishes'
  }

  return (
    <div className="space-y-12">
      {categoryOrder.map((category) => {
        const categoryProducts = groupedProducts[category]
        if (!categoryProducts || categoryProducts.length === 0) return null

        return (
          <div key={category}>
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              {categoryNames[category as keyof typeof categoryNames]}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categoryProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        )
      })}
    </div>
  )
}
