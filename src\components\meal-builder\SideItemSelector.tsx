'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { PlusIcon, MinusIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface Side {
  id: string
  name: string
  description: string
  category: string
  price: number
  imageUrl: string
  isAvailable: boolean
}

interface MealBuilderItem {
  id: string
  type: 'product' | 'side'
  name: string
  price: number
  quantity: number
  imageUrl?: string
  category?: string
}

interface SideItemSelectorProps {
  sides: Side[]
  selectedSides: MealBuilderItem[]
  onAddSide: (side: Side, quantity?: number) => void
  onRemoveSide: (sideId: string) => void
  onUpdateQuantity: (sideId: string, quantity: number) => void
}

export function SideItemSelector({
  sides,
  selectedSides,
  onAddSide,
  onRemoveSide,
  onUpdateQuantity
}: SideItemSelectorProps) {
  // Group sides by category
  const groupedSides = sides.reduce((acc, side) => {
    if (!acc[side.category]) {
      acc[side.category] = []
    }
    acc[side.category].push(side)
    return acc
  }, {} as Record<string, Side[]>)

  const categoryOrder = ['chutney', 'curry', 'vegetable']
  const categoryNames = {
    chutney: 'Traditional Chutneys',
    curry: 'Curries & Soups',
    vegetable: 'Vegetable Sides'
  }

  const categoryIcons = {
    chutney: '🥥',
    curry: '🍲',
    vegetable: '🥬'
  }

  const categoryDescriptions = {
    chutney: 'Fresh, flavorful chutneys made daily with authentic ingredients',
    curry: 'Rich, aromatic curries and traditional soups',
    vegetable: 'Seasonal vegetables prepared with traditional spices'
  }

  const getSelectedSide = (sideId: string) => {
    return selectedSides.find(side => side.id === sideId)
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          Step 2: Add Traditional Sides
        </h3>
        <p className="text-gray-600">
          Enhance your meal with authentic accompaniments
        </p>
      </div>

      {/* Selected Sides Summary */}
      {selectedSides.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-secondary-50 border border-secondary-200 rounded-lg p-4"
        >
          <h4 className="font-semibold text-secondary-800 mb-3">
            Selected Sides ({selectedSides.length})
          </h4>
          <div className="flex flex-wrap gap-2">
            {selectedSides.map((side) => (
              <div
                key={side.id}
                className="bg-white rounded-lg px-3 py-2 flex items-center space-x-2 shadow-sm border"
              >
                <span className="text-sm font-medium">{side.name}</span>
                <span className="text-xs text-gray-500">×{side.quantity}</span>
                <button
                  onClick={() => onRemoveSide(side.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {categoryOrder.map((category) => {
        const categorySides = groupedSides[category]
        if (!categorySides || categorySides.length === 0) return null

        return (
          <motion.div
            key={category}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="space-y-4"
          >
            {/* Category Header */}
            <div className="text-center py-4 bg-gray-50 rounded-lg">
              <div className="text-3xl mb-2">
                {categoryIcons[category as keyof typeof categoryIcons]}
              </div>
              <h4 className="text-lg font-semibold text-gray-900">
                {categoryNames[category as keyof typeof categoryNames]}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {categoryDescriptions[category as keyof typeof categoryDescriptions]}
              </p>
            </div>

            {/* Sides Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {categorySides.map((side) => {
                const selectedSide = getSelectedSide(side.id)
                const isSelected = !!selectedSide
                
                return (
                  <motion.div
                    key={side.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`
                      card-hover transition-all duration-200
                      ${isSelected 
                        ? 'ring-2 ring-secondary-500 bg-secondary-50' 
                        : 'hover:shadow-lg'
                      }
                      ${!side.isAvailable ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    `}
                  >
                    {/* Side Image */}
                    <div className="relative h-24 bg-gray-200 rounded-t-lg overflow-hidden">
                      {side.imageUrl ? (
                        <Image
                          src={side.imageUrl}
                          alt={side.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No Image</span>
                        </div>
                      )}
                      
                      {!side.isAvailable && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <span className="text-white text-xs font-medium">Out of Stock</span>
                        </div>
                      )}

                      {/* Quantity Badge */}
                      {isSelected && selectedSide && (
                        <div className="absolute top-2 right-2 bg-secondary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                          {selectedSide.quantity}
                        </div>
                      )}
                    </div>

                    {/* Side Info */}
                    <div className="p-3">
                      <h5 className="font-semibold text-gray-900 text-sm mb-1">
                        {side.name}
                      </h5>
                      <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                        {side.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-bold text-gray-900">
                          ${side.price.toFixed(2)}
                        </span>
                        
                        {isSelected && selectedSide ? (
                          <div className="flex items-center space-x-1 bg-white rounded-lg px-2 py-1 border">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                onUpdateQuantity(side.id, Math.max(0, selectedSide.quantity - 1))
                              }}
                              className="p-1 hover:bg-gray-100 rounded"
                            >
                              <MinusIcon className="h-3 w-3" />
                            </button>
                            <span className="font-medium text-xs min-w-[16px] text-center">
                              {selectedSide.quantity}
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                onUpdateQuantity(side.id, selectedSide.quantity + 1)
                              }}
                              className="p-1 hover:bg-gray-100 rounded"
                            >
                              <PlusIcon className="h-3 w-3" />
                            </button>
                          </div>
                        ) : (
                          <button
                            onClick={() => side.isAvailable && onAddSide(side)}
                            disabled={!side.isAvailable}
                            className={`
                              flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-medium transition-colors
                              ${side.isAvailable
                                ? 'bg-secondary-500 hover:bg-secondary-600 text-white'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              }
                            `}
                          >
                            <PlusIcon className="h-3 w-3" />
                            <span>Add</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        )
      })}

      {/* Helpful Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-800 mb-2">💡 Traditional Pairing Tips</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p><strong>For Idlis:</strong> Coconut chutney + Sambar is the classic combination</p>
          <p><strong>For Dosas:</strong> Try multiple chutneys with sambar for variety</p>
          <p><strong>For Rice:</strong> Rasam and vegetable curry create a complete meal</p>
        </div>
      </div>
    </div>
  )
}
