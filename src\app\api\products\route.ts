import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    // Fetch all available products
    const products = await prisma.product.findMany({
      where: {
        isAvailable: true
      },
      orderBy: [
        { category: 'asc' },
        { name: 'asc' }
      ]
    })

    // Fetch all available sides
    const sides = await prisma.side.findMany({
      where: {
        isAvailable: true
      },
      orderBy: [
        { category: 'asc' },
        { name: 'asc' }
      ]
    })

    // Transform the data to match our frontend interface
    const transformedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      category: product.category,
      variety: product.variety,
      basePrice: Number(product.basePrice),
      imageUrl: product.imageUrl,
      isAvailable: product.isAvailable
    }))

    const transformedSides = sides.map(side => ({
      id: side.id,
      name: side.name,
      description: side.description,
      category: side.category,
      price: Number(side.price),
      imageUrl: side.imageUrl,
      isAvailable: side.isAvailable
    }))

    return NextResponse.json({
      products: transformedProducts,
      sides: transformedSides
    })
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    )
  }
}
