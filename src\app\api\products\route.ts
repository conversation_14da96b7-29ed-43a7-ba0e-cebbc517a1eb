import { NextResponse } from 'next/server'
import { mockProducts, mockSides } from '@/lib/mock-data'

export async function GET() {
  try {
    // Simulate a small delay to mimic database call
    await new Promise(resolve => setTimeout(resolve, 100))

    // Return mock data
    return NextResponse.json({
      products: mockProducts,
      sides: mockSides
    })
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    )
  }
}
