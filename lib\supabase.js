// Supabase client configuration for FreshSteam
import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key'

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database helper functions
export const db = {
  // Products
  async getProducts() {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('is_available', true)
      .order('display_order')
    
    if (error) throw error
    return data
  },

  async getProduct(id) {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        sides:product_sides(
          side:sides(*)
        )
      `)
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Sides
  async getSides() {
    const { data, error } = await supabase
      .from('sides')
      .select('*')
      .eq('is_available', true)
      .order('display_order')
    
    if (error) throw error
    return data
  },

  // Traditional Combos
  async getTraditionalCombos() {
    const { data, error } = await supabase
      .from('traditional_combos')
      .select(`
        *,
        combo_items:traditional_combo_items(
          product:products(*),
          side:sides(*)
        )
      `)
      .eq('is_active', true)
      .order('display_order')
    
    if (error) throw error
    return data
  },

  // Cart Sessions
  async createCartSession() {
    const { data, error } = await supabase
      .from('cart_sessions')
      .insert({
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async getCartSession(sessionId) {
    const { data, error } = await supabase
      .from('cart_sessions')
      .select(`
        *,
        items:cart_items(
          *,
          product:products(*),
          side:sides(*)
        )
      `)
      .eq('id', sessionId)
      .single()
    
    if (error) throw error
    return data
  },

  async addToCart(sessionId, item) {
    const { data, error } = await supabase
      .from('cart_items')
      .insert({
        cart_session_id: sessionId,
        product_id: item.product_id,
        side_id: item.side_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        customizations: item.customizations
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async updateCartItem(itemId, updates) {
    const { data, error } = await supabase
      .from('cart_items')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', itemId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async removeFromCart(itemId) {
    const { error } = await supabase
      .from('cart_items')
      .delete()
      .eq('id', itemId)
    
    if (error) throw error
    return true
  },

  // Orders
  async createOrder(orderData) {
    const { data, error } = await supabase
      .from('orders')
      .insert({
        cart_session_id: orderData.cart_session_id,
        customer_email: orderData.customer_email,
        customer_phone: orderData.customer_phone,
        delivery_address: orderData.delivery_address,
        subtotal: orderData.subtotal,
        tax_amount: orderData.tax_amount,
        delivery_fee: orderData.delivery_fee,
        total_amount: orderData.total_amount,
        payment_intent_id: orderData.payment_intent_id,
        status: 'pending',
        created_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async updateOrderStatus(orderId, status, paymentData = {}) {
    const { data, error } = await supabase
      .from('orders')
      .update({
        status,
        payment_status: paymentData.payment_status,
        payment_method: paymentData.payment_method,
        stripe_payment_id: paymentData.stripe_payment_id,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Admin functions
  async updateProduct(productId, updates) {
    const { data, error } = await supabase
      .from('products')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', productId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async updateSide(sideId, updates) {
    const { data, error } = await supabase
      .from('sides')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', sideId)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

// Real-time subscriptions
export const subscriptions = {
  subscribeToProducts(callback) {
    return supabase
      .channel('products')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'products' }, 
        callback
      )
      .subscribe()
  },

  subscribeToOrders(callback) {
    return supabase
      .channel('orders')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'orders' }, 
        callback
      )
      .subscribe()
  }
}
