// Admin inventory management endpoint
import jwt from 'jsonwebtoken'
import { supabase } from '../../../lib/supabase'

// Middleware to verify admin token
function verifyAdminToken(req) {
  const authHeader = req.headers.authorization
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('No valid authorization token provided')
  }

  const token = authHeader.substring(7)
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret')
    return decoded
  } catch (error) {
    throw new Error('Invalid or expired token')
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, POST, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  try {
    // Verify admin authentication
    const adminData = verifyAdminToken(req)

    switch (req.method) {
      case 'GET':
        await handleGetInventory(req, res)
        break
      case 'PUT':
        await handleUpdateItem(req, res)
        break
      case 'POST':
        await handleCreateItem(req, res)
        break
      case 'DELETE':
        await handleDeleteItem(req, res)
        break
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'POST', 'DELETE'])
        res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('Admin inventory API error:', error)
    
    if (error.message.includes('token') || error.message.includes('authorization')) {
      res.status(401).json({
        error: 'Unauthorized',
        message: error.message
      })
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      })
    }
  }
}

async function handleGetInventory(req, res) {
  const { type = 'all', category, available } = req.query

  try {
    let productsData = []
    let sidesData = []

    if (type === 'all' || type === 'products') {
      const productsQuery = supabase
        .from('products')
        .select(`
          id,
          name,
          description,
          cultural_context,
          base_price,
          image_url,
          ingredients,
          allergens,
          dietary_info,
          spice_level,
          prep_time,
          is_available,
          is_featured,
          display_order,
          created_at,
          updated_at,
          category:categories(id, name)
        `)
        .order('display_order')

      if (available !== undefined) {
        productsQuery.eq('is_available', available === 'true')
      }

      const { data: products, error: productsError } = await productsQuery
      if (productsError) throw productsError
      productsData = products || []
    }

    if (type === 'all' || type === 'sides') {
      const sidesQuery = supabase
        .from('sides')
        .select(`
          id,
          name,
          description,
          cultural_context,
          price,
          image_url,
          category,
          ingredients,
          allergens,
          dietary_info,
          spice_level,
          is_available,
          display_order,
          created_at,
          updated_at
        `)
        .order('display_order')

      if (available !== undefined) {
        sidesQuery.eq('is_available', available === 'true')
      }

      const { data: sides, error: sidesError } = await sidesQuery
      if (sidesError) throw sidesError
      sidesData = sides || []
    }

    // Transform data for admin interface
    const inventory = {
      products: productsData.map(product => ({
        id: product.id,
        type: 'product',
        name: product.name,
        description: product.description,
        culturalContext: product.cultural_context,
        price: parseFloat(product.base_price),
        imageUrl: product.image_url,
        category: product.category?.name || 'Unknown',
        ingredients: product.ingredients || [],
        allergens: product.allergens || [],
        dietaryInfo: product.dietary_info || {},
        spiceLevel: product.spice_level,
        prepTime: product.prep_time,
        isAvailable: product.is_available,
        isFeatured: product.is_featured,
        displayOrder: product.display_order,
        createdAt: product.created_at,
        updatedAt: product.updated_at
      })),
      
      sides: sidesData.map(side => ({
        id: side.id,
        type: 'side',
        name: side.name,
        description: side.description,
        culturalContext: side.cultural_context,
        price: parseFloat(side.price),
        imageUrl: side.image_url,
        category: side.category,
        ingredients: side.ingredients || [],
        allergens: side.allergens || [],
        dietaryInfo: side.dietary_info || {},
        spiceLevel: side.spice_level,
        isAvailable: side.is_available,
        displayOrder: side.display_order,
        createdAt: side.created_at,
        updatedAt: side.updated_at
      }))
    }

    res.status(200).json({
      success: true,
      data: inventory,
      counts: {
        totalProducts: productsData.length,
        availableProducts: productsData.filter(p => p.is_available).length,
        totalSides: sidesData.length,
        availableSides: sidesData.filter(s => s.is_available).length
      }
    })

  } catch (error) {
    throw new Error(`Failed to fetch inventory: ${error.message}`)
  }
}

async function handleUpdateItem(req, res) {
  const { itemId, itemType, updates } = req.body

  if (!itemId || !itemType || !updates) {
    return res.status(400).json({
      error: 'Item ID, type, and updates are required'
    })
  }

  if (!['product', 'side'].includes(itemType)) {
    return res.status(400).json({
      error: 'Invalid item type. Must be "product" or "side"'
    })
  }

  try {
    const table = itemType === 'product' ? 'products' : 'sides'
    const updateData = {
      updated_at: new Date().toISOString()
    }

    // Map frontend field names to database field names
    if (updates.name) updateData.name = updates.name
    if (updates.description) updateData.description = updates.description
    if (updates.culturalContext) updateData.cultural_context = updates.culturalContext
    if (updates.price !== undefined) {
      if (itemType === 'product') {
        updateData.base_price = updates.price
      } else {
        updateData.price = updates.price
      }
    }
    if (updates.imageUrl) updateData.image_url = updates.imageUrl
    if (updates.ingredients) updateData.ingredients = updates.ingredients
    if (updates.allergens) updateData.allergens = updates.allergens
    if (updates.dietaryInfo) updateData.dietary_info = updates.dietaryInfo
    if (updates.spiceLevel !== undefined) updateData.spice_level = updates.spiceLevel
    if (updates.isAvailable !== undefined) updateData.is_available = updates.isAvailable
    if (updates.displayOrder !== undefined) updateData.display_order = updates.displayOrder
    
    // Product-specific fields
    if (itemType === 'product') {
      if (updates.prepTime !== undefined) updateData.prep_time = updates.prepTime
      if (updates.isFeatured !== undefined) updateData.is_featured = updates.isFeatured
    }
    
    // Side-specific fields
    if (itemType === 'side' && updates.category) {
      updateData.category = updates.category
    }

    const { data: updatedItem, error } = await supabase
      .from(table)
      .update(updateData)
      .eq('id', itemId)
      .select()
      .single()

    if (error) throw error

    res.status(200).json({
      success: true,
      data: updatedItem,
      message: `${itemType} updated successfully`
    })

  } catch (error) {
    throw new Error(`Failed to update ${itemType}: ${error.message}`)
  }
}

async function handleCreateItem(req, res) {
  const { itemType, itemData } = req.body

  if (!itemType || !itemData) {
    return res.status(400).json({
      error: 'Item type and data are required'
    })
  }

  if (!['product', 'side'].includes(itemType)) {
    return res.status(400).json({
      error: 'Invalid item type. Must be "product" or "side"'
    })
  }

  try {
    const table = itemType === 'product' ? 'products' : 'sides'
    const insertData = {
      name: itemData.name,
      description: itemData.description,
      cultural_context: itemData.culturalContext,
      image_url: itemData.imageUrl,
      ingredients: itemData.ingredients || [],
      allergens: itemData.allergens || [],
      dietary_info: itemData.dietaryInfo || {},
      spice_level: itemData.spiceLevel || 1,
      is_available: itemData.isAvailable !== false,
      display_order: itemData.displayOrder || 0
    }

    if (itemType === 'product') {
      insertData.category_id = itemData.categoryId
      insertData.base_price = itemData.price
      insertData.prep_time = itemData.prepTime
      insertData.is_featured = itemData.isFeatured || false
    } else {
      insertData.price = itemData.price
      insertData.category = itemData.category || 'accompaniment'
    }

    const { data: newItem, error } = await supabase
      .from(table)
      .insert(insertData)
      .select()
      .single()

    if (error) throw error

    res.status(201).json({
      success: true,
      data: newItem,
      message: `${itemType} created successfully`
    })

  } catch (error) {
    throw new Error(`Failed to create ${itemType}: ${error.message}`)
  }
}

async function handleDeleteItem(req, res) {
  const { itemId, itemType } = req.query

  if (!itemId || !itemType) {
    return res.status(400).json({
      error: 'Item ID and type are required'
    })
  }

  try {
    const table = itemType === 'product' ? 'products' : 'sides'
    
    // Soft delete by setting is_available to false
    const { data: deletedItem, error } = await supabase
      .from(table)
      .update({ 
        is_available: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', itemId)
      .select()
      .single()

    if (error) throw error

    res.status(200).json({
      success: true,
      data: deletedItem,
      message: `${itemType} deleted successfully`
    })

  } catch (error) {
    throw new Error(`Failed to delete ${itemType}: ${error.message}`)
  }
}
