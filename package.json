{"name": "freshsteam-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "node -r esbuild-register prisma/seed.ts"}, "dependencies": {"next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "@prisma/client": "^5.15.0", "bcryptjs": "^2.4.3", "zustand": "^4.5.4", "clsx": "^2.1.1", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "tailwind-merge": "^2.4.0", "framer-motion": "^11.3.8", "react-hot-toast": "^2.4.1", "uuid": "^10.0.0"}, "devDependencies": {"typescript": "^5.5.3", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "tailwindcss": "^3.4.6", "autoprefixer": "^10.4.19", "postcss": "^8.4.39", "prisma": "^5.15.0", "esbuild-register": "^3.5.0", "@tailwindcss/forms": "^0.5.7"}}