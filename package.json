{"name": "freshsteam-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"next": "14.2.5", "react": "18.3.1", "react-dom": "18.3.1", "@prisma/client": "5.15.0", "zustand": "4.5.4", "clsx": "2.1.1"}, "devDependencies": {"typescript": "5.5.3", "@types/node": "20.14.10", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "eslint": "8.57.0", "eslint-config-next": "14.2.5", "tailwindcss": "3.4.6", "autoprefixer": "10.4.19", "postcss": "8.4.39", "prisma": "5.15.0", "tsx": "4.16.2"}}