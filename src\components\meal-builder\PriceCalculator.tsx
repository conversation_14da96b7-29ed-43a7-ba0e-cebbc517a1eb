'use client'

// Simple icon components
const ShoppingCartIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
  </svg>
)

const TrashIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
  </svg>
)

const SparklesIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
  </svg>
)

// Simple motion component
const motion = {
  div: ({ children, className, onClick, ...props }: any) => (
    <div className={className} onClick={onClick} {...props}>
      {children}
    </div>
  ),
  button: ({ children, className, onClick, ...props }: any) => (
    <button className={className} onClick={onClick} {...props}>
      {children}
    </button>
  )
}

interface MealBuilderItem {
  id: string
  type: 'product' | 'side'
  name: string
  price: number
  quantity: number
  imageUrl?: string
  category?: string
  variety?: string
}

interface PriceCalculatorProps {
  selectedMain: MealBuilderItem | null
  selectedSides: MealBuilderItem[]
  totalPrice: number
  onAddToCart: () => void
  onClear: () => void
}

export function PriceCalculator({
  selectedMain,
  selectedSides,
  totalPrice,
  onAddToCart,
  onClear
}: PriceCalculatorProps) {
  // Calculate individual totals
  const mainTotal = selectedMain ? selectedMain.price * selectedMain.quantity : 0
  const sidesTotal = selectedSides.reduce((total, side) => total + (side.price * side.quantity), 0)
  
  // Calculate potential savings/discounts
  const hasComboDiscount = selectedMain && selectedSides.length >= 2
  const comboDiscount = hasComboDiscount ? totalPrice * 0.05 : 0 // 5% discount for combo
  const finalTotal = totalPrice - comboDiscount

  // Calculate total items
  const totalItems = (selectedMain?.quantity || 0) + selectedSides.reduce((total, side) => total + side.quantity, 0)

  const canAddToCart = selectedMain !== null

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white border border-gray-200 rounded-lg p-6 space-y-6 h-fit"
    >
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          💰 Price Breakdown
        </h3>
        <p className="text-sm text-gray-600">
          Real-time pricing with traditional combo benefits
        </p>
      </div>

      {/* Price Breakdown */}
      <div className="space-y-4">
        {/* Main Item Total */}
        {selectedMain && (
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <div>
              <p className="font-medium text-gray-900">Main Dish</p>
              <p className="text-sm text-gray-600">
                {selectedMain.quantity}x {selectedMain.name}
              </p>
            </div>
            <p className="font-semibold text-gray-900">
              ${mainTotal.toFixed(2)}
            </p>
          </div>
        )}

        {/* Sides Total */}
        {selectedSides.length > 0 && (
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <div>
              <p className="font-medium text-gray-900">Sides</p>
              <p className="text-sm text-gray-600">
                {selectedSides.length} item{selectedSides.length > 1 ? 's' : ''} selected
              </p>
            </div>
            <p className="font-semibold text-gray-900">
              ${sidesTotal.toFixed(2)}
            </p>
          </div>
        )}

        {/* Subtotal */}
        {(selectedMain || selectedSides.length > 0) && (
          <div className="flex justify-between items-center py-2">
            <p className="font-medium text-gray-700">Subtotal</p>
            <p className="font-semibold text-gray-900">
              ${totalPrice.toFixed(2)}
            </p>
          </div>
        )}

        {/* Combo Discount */}
        {hasComboDiscount && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex justify-between items-center py-2 bg-green-50 px-3 rounded-lg border border-green-200"
          >
            <div className="flex items-center space-x-2">
              <SparklesIcon className="h-4 w-4 text-green-600" />
              <div>
                <p className="font-medium text-green-800 text-sm">Traditional Combo Discount</p>
                <p className="text-xs text-green-600">5% off for authentic meal combinations</p>
              </div>
            </div>
            <p className="font-semibold text-green-700">
              -${comboDiscount.toFixed(2)}
            </p>
          </motion.div>
        )}

        {/* Total */}
        {(selectedMain || selectedSides.length > 0) && (
          <div className="flex justify-between items-center py-3 border-t-2 border-gray-200">
            <div>
              <p className="text-lg font-bold text-gray-900">Total</p>
              <p className="text-sm text-gray-600">
                {totalItems} item{totalItems > 1 ? 's' : ''}
              </p>
            </div>
            <p className="text-2xl font-bold text-primary-600">
              ${finalTotal.toFixed(2)}
            </p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        {canAddToCart ? (
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={onAddToCart}
            className="w-full bg-primary-500 hover:bg-primary-600 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl"
          >
            <ShoppingCartIcon className="h-5 w-5" />
            <span>Add Meal to Cart</span>
          </motion.button>
        ) : (
          <div className="w-full bg-gray-100 text-gray-500 font-semibold py-4 px-6 rounded-lg text-center">
            Select a main dish to continue
          </div>
        )}

        {(selectedMain || selectedSides.length > 0) && (
          <button
            onClick={onClear}
            className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <TrashIcon className="h-4 w-4" />
            <span>Clear Meal</span>
          </button>
        )}
      </div>

      {/* Pricing Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-2">
        <h4 className="font-semibold text-blue-800 text-sm">💡 Pricing Benefits</h4>
        <div className="text-xs text-blue-700 space-y-1">
          <p>• <strong>Combo Discount:</strong> 5% off when you order main + 2 or more sides</p>
          <p>• <strong>Fresh Guarantee:</strong> All items prepared fresh to order</p>
          <p>• <strong>Authentic Value:</strong> Traditional recipes at modern convenience</p>
        </div>
      </div>

      {/* Nutritional Info Teaser */}
      {selectedMain && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-semibold text-green-800 text-sm mb-2">🌱 Nutritional Highlights</h4>
          <div className="text-xs text-green-700 space-y-1">
            {selectedMain.category === 'idli' && (
              <>
                <p>• High in protein from fermented rice and lentils</p>
                <p>• Probiotic benefits from natural fermentation</p>
                <p>• Low in fat, easy to digest</p>
              </>
            )}
            {selectedMain.category === 'dosa' && (
              <>
                <p>• Rich in carbohydrates for sustained energy</p>
                <p>• Contains essential amino acids</p>
                <p>• Crispy texture with minimal oil</p>
              </>
            )}
            {selectedMain.category === 'rice' && (
              <>
                <p>• Balanced meal with vegetables and spices</p>
                <p>• Good source of fiber and nutrients</p>
                <p>• Traditional spice blend aids digestion</p>
              </>
            )}
          </div>
        </div>
      )}
    </motion.div>
  )
}
