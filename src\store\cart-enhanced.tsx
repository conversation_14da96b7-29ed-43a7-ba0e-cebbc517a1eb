'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// Enhanced cart item interface for Phase 3
export interface EnhancedCartItem {
  id: string
  type: 'product' | 'side' | 'combo'
  productId?: string
  sideId?: string
  name: string
  price: number
  quantity: number
  imageUrl?: string
  category?: string
  variety?: string
  customizations?: Record<string, any>
  // Combo specific fields
  originalPrice?: number
  discountAmount?: number
  comboItems?: any[]
}

interface EnhancedCartState {
  items: EnhancedCartItem[]
  sessionId: string | null
  isOpen: boolean
  isLoading: boolean
  error: string | null
  
  // Backend integration actions
  initializeSession: () => Promise<void>
  loadCart: () => Promise<void>
  addItemToBackend: (item: Omit<EnhancedCartItem, 'id'>) => Promise<void>
  addComboToBackend: (comboId: string, customizations?: Record<string, any>) => Promise<void>
  removeItemFromBackend: (itemId: string) => Promise<void>
  updateQuantityInBackend: (itemId: string, quantity: number) => Promise<void>
  
  // Local actions (fallback)
  addItem: (item: Omit<EnhancedCartItem, 'quantity'> & { quantity?: number }) => void
  removeItem: (id: string) => void
  updateQuantity: (id: string, quantity: number) => void
  clearCart: () => void
  toggleCart: () => void
  
  // Enhanced getters
  getTotalItems: () => number
  getTotalPrice: () => number
  getSubtotal: () => number
  getTaxAmount: () => number
  getDeliveryFee: () => number
  getTotal: () => number
  getSavings: () => number
}

export const useEnhancedCart = create<EnhancedCartState>()(
  persist(
    (set, get) => ({
      items: [],
      sessionId: null,
      isOpen: false,
      isLoading: false,
      error: null,
      
      // Backend integration methods
      initializeSession: async () => {
        try {
          set({ isLoading: true, error: null })
          
          // Dynamic import for API (client-side only)
          const { default: api } = await import('../../lib/api')
          
          if (!get().sessionId) {
            const response = await api.createCartSession()
            set({ sessionId: response.data.sessionId })
          }
          
          await get().loadCart()
        } catch (error: any) {
          set({ error: error.message })
          console.error('Failed to initialize cart session:', error)
        } finally {
          set({ isLoading: false })
        }
      },
      
      loadCart: async () => {
        const sessionId = get().sessionId
        if (!sessionId) return
        
        try {
          set({ isLoading: true, error: null })
          
          const { default: api } = await import('../../lib/api')
          const response = await api.getCart(sessionId)
          
          // Transform API response to match our cart item structure
          const items = response.data.items.map((item: any) => ({
            id: item.id,
            type: item.customizations?.type || (item.productId ? 'product' : 'side'),
            productId: item.productId,
            sideId: item.sideId,
            name: item.productName || item.sideName || item.customizations?.comboName || 'Unknown Item',
            price: item.unitPrice,
            quantity: item.quantity,
            imageUrl: item.imageUrl,
            customizations: item.customizations,
            originalPrice: item.customizations?.originalPrice,
            discountAmount: item.customizations?.discountAmount,
            comboItems: item.customizations?.items
          }))
          
          set({ items })
        } catch (error: any) {
          set({ error: error.message })
          console.error('Failed to load cart:', error)
        } finally {
          set({ isLoading: false })
        }
      },
      
      addItemToBackend: async (item) => {
        const sessionId = get().sessionId
        if (!sessionId) {
          await get().initializeSession()
          return get().addItemToBackend(item)
        }
        
        try {
          set({ isLoading: true, error: null })
          
          const { default: api } = await import('../../lib/api')
          
          const cartItem = {
            productId: item.productId,
            sideId: item.sideId,
            quantity: item.quantity,
            unitPrice: item.price,
            customizations: item.customizations
          }
          
          await api.addToCart(sessionId, cartItem)
          await get().loadCart() // Refresh cart from server
        } catch (error: any) {
          set({ error: error.message })
          console.error('Failed to add item to cart:', error)
          // Fallback to local storage
          get().addItem(item)
        } finally {
          set({ isLoading: false })
        }
      },
      
      addComboToBackend: async (comboId, customizations = {}) => {
        const sessionId = get().sessionId
        if (!sessionId) {
          await get().initializeSession()
          return get().addComboToBackend(comboId, customizations)
        }
        
        try {
          set({ isLoading: true, error: null })
          
          const { default: api } = await import('../../lib/api')
          await api.addComboToCart(sessionId, comboId, customizations)
          await get().loadCart() // Refresh cart from server
        } catch (error: any) {
          set({ error: error.message })
          console.error('Failed to add combo to cart:', error)
        } finally {
          set({ isLoading: false })
        }
      },
      
      removeItemFromBackend: async (itemId) => {
        try {
          set({ isLoading: true, error: null })
          
          const { default: api } = await import('../../lib/api')
          await api.removeFromCart(itemId)
          await get().loadCart() // Refresh cart from server
        } catch (error: any) {
          set({ error: error.message })
          console.error('Failed to remove item from cart:', error)
          // Fallback to local removal
          get().removeItem(itemId)
        } finally {
          set({ isLoading: false })
        }
      },
      
      updateQuantityInBackend: async (itemId, quantity) => {
        if (quantity <= 0) {
          return get().removeItemFromBackend(itemId)
        }
        
        try {
          set({ isLoading: true, error: null })
          
          const { default: api } = await import('../../lib/api')
          await api.updateCartItem(itemId, { quantity })
          await get().loadCart() // Refresh cart from server
        } catch (error: any) {
          set({ error: error.message })
          console.error('Failed to update cart item:', error)
          // Fallback to local update
          get().updateQuantity(itemId, quantity)
        } finally {
          set({ isLoading: false })
        }
      },
      
      // Local actions (fallback and backward compatibility)
      addItem: (item) => {
        const existingItem = get().items.find(i => i.id === item.id)

        if (existingItem) {
          set(state => ({
            items: state.items.map(i =>
              i.id === item.id
                ? { ...i, quantity: i.quantity + (item.quantity || 1) }
                : i
            )
          }))
        } else {
          set(state => ({
            items: [...state.items, { ...item, quantity: item.quantity || 1 }]
          }))
        }
      },

      removeItem: (id) => {
        set(state => ({
          items: state.items.filter(item => item.id !== id)
        }))
      },

      updateQuantity: (id, quantity) => {
        if (quantity <= 0) {
          get().removeItem(id)
          return
        }

        set(state => ({
          items: state.items.map(item =>
            item.id === id ? { ...item, quantity } : item
          )
        }))
      },

      clearCart: () => {
        set({ items: [], sessionId: null })
      },

      toggleCart: () => {
        set(state => ({ isOpen: !state.isOpen }))
      },

      // Enhanced getters
      getTotalItems: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0)
      },

      getTotalPrice: () => {
        return get().items.reduce((total, item) => total + (item.price * item.quantity), 0)
      },
      
      getSubtotal: () => {
        return get().getTotalPrice()
      },
      
      getTaxAmount: () => {
        const subtotal = get().getSubtotal()
        return subtotal * 0.08 // 8% tax
      },
      
      getDeliveryFee: () => {
        const subtotal = get().getSubtotal()
        return subtotal >= 25 ? 0 : 3.99 // Free delivery over $25
      },
      
      getTotal: () => {
        return get().getSubtotal() + get().getTaxAmount() + get().getDeliveryFee()
      },
      
      getSavings: () => {
        return get().items.reduce((total, item) => {
          if (item.type === 'combo' && item.discountAmount) {
            return total + (item.discountAmount * item.quantity)
          }
          return total
        }, 0)
      }
    }),
    {
      name: 'freshsteam-enhanced-cart',
      partialize: (state) => ({ 
        sessionId: state.sessionId,
        items: state.items // Keep items for offline viewing
      })
    }
  )
)
