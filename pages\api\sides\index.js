// API endpoint for sides
import { db } from '../../../lib/supabase'

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  try {
    switch (req.method) {
      case 'GET':
        await handleGet(req, res)
        break
      case 'POST':
        await handlePost(req, res)
        break
      case 'PUT':
        await handlePut(req, res)
        break
      case 'DELETE':
        await handleDelete(req, res)
        break
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE'])
        res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('API Error:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    })
  }
}

async function handleGet(req, res) {
  const { category, available } = req.query

  try {
    let sides = await db.getSides()

    // Apply filters
    if (category) {
      sides = sides.filter(s => s.category?.toLowerCase() === category.toLowerCase())
    }
    
    if (available === 'false') {
      sides = sides.filter(s => !s.is_available)
    }

    // Transform data for frontend compatibility
    const transformedSides = sides.map(side => ({
      id: side.id,
      name: side.name,
      description: side.description,
      culturalContext: side.cultural_context,
      category: side.category,
      price: parseFloat(side.price),
      imageUrl: side.image_url,
      ingredients: side.ingredients || [],
      allergens: side.allergens || [],
      dietaryInfo: side.dietary_info || {},
      spiceLevel: side.spice_level || 1,
      isAvailable: side.is_available,
      displayOrder: side.display_order,
      createdAt: side.created_at,
      updatedAt: side.updated_at
    }))

    res.status(200).json({
      success: true,
      data: transformedSides,
      count: transformedSides.length
    })
  } catch (error) {
    throw new Error(`Failed to fetch sides: ${error.message}`)
  }
}

async function handlePost(req, res) {
  // Admin only - create new side
  const {
    name,
    description,
    culturalContext,
    price,
    imageUrl,
    category,
    ingredients,
    allergens,
    dietaryInfo,
    spiceLevel
  } = req.body

  if (!name || !price) {
    return res.status(400).json({
      error: 'Missing required fields',
      required: ['name', 'price']
    })
  }

  try {
    const { data, error } = await supabase
      .from('sides')
      .insert({
        name,
        description,
        cultural_context: culturalContext,
        price,
        image_url: imageUrl,
        category: category || 'accompaniment',
        ingredients: ingredients || [],
        allergens: allergens || [],
        dietary_info: dietaryInfo || {},
        spice_level: spiceLevel || 1,
        is_available: true
      })
      .select()
      .single()

    if (error) throw error

    res.status(201).json({
      success: true,
      data: data,
      message: 'Side created successfully'
    })
  } catch (error) {
    throw new Error(`Failed to create side: ${error.message}`)
  }
}

async function handlePut(req, res) {
  // Admin only - update side
  const { id } = req.query
  const updates = req.body

  if (!id) {
    return res.status(400).json({
      error: 'Side ID is required'
    })
  }

  try {
    const updatedSide = await db.updateSide(id, {
      name: updates.name,
      description: updates.description,
      cultural_context: updates.culturalContext,
      price: updates.price,
      image_url: updates.imageUrl,
      category: updates.category,
      ingredients: updates.ingredients,
      allergens: updates.allergens,
      dietary_info: updates.dietaryInfo,
      spice_level: updates.spiceLevel,
      is_available: updates.isAvailable
    })

    res.status(200).json({
      success: true,
      data: updatedSide,
      message: 'Side updated successfully'
    })
  } catch (error) {
    throw new Error(`Failed to update side: ${error.message}`)
  }
}

async function handleDelete(req, res) {
  // Admin only - soft delete side
  const { id } = req.query

  if (!id) {
    return res.status(400).json({
      error: 'Side ID is required'
    })
  }

  try {
    const updatedSide = await db.updateSide(id, {
      is_available: false
    })

    res.status(200).json({
      success: true,
      data: updatedSide,
      message: 'Side deleted successfully'
    })
  } catch (error) {
    throw new Error(`Failed to delete side: ${error.message}`)
  }
}
