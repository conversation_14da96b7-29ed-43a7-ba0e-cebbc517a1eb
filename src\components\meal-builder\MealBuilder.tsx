'use client'

import { useState, useEffect } from 'react'
import { useCart } from '@/store/cart'
import { MainItemSelector } from './MainItemSelector'
import { SideItemSelector } from './SideItemSelector'
import { MealSummary } from './MealSummary'
import { PriceCalculator } from './PriceCalculator'
import { TraditionalCombos } from './TraditionalCombos'

// Simple fallback components
const motion = {
  div: ({ children, className, onClick, ...props }: any) => (
    <div className={className} onClick={onClick} {...props}>
      {children}
    </div>
  )
}

const AnimatePresence = ({ children }: { children: React.ReactNode }) => <>{children}</>

const toast = {
  success: (message: string) => console.log('✅', message),
  error: (message: string) => console.log('❌', message)
}

interface Product {
  id: string
  name: string
  description: string
  category: string
  variety: string
  basePrice: number
  imageUrl: string
  isAvailable: boolean
}

interface Side {
  id: string
  name: string
  description: string
  category: string
  price: number
  imageUrl: string
  isAvailable: boolean
}

interface MealBuilderItem {
  id: string
  type: 'product' | 'side'
  name: string
  price: number
  quantity: number
  imageUrl?: string
  category?: string
  variety?: string
}

interface MealBuilderState {
  selectedMain: MealBuilderItem | null
  selectedSides: MealBuilderItem[]
  totalPrice: number
}

export function MealBuilder() {
  const [products, setProducts] = useState<Product[]>([])
  const [sides, setSides] = useState<Side[]>([])
  const [loading, setLoading] = useState(true)
  const [mealState, setMealState] = useState<MealBuilderState>({
    selectedMain: null,
    selectedSides: [],
    totalPrice: 0
  })
  const [isBuilderOpen, setIsBuilderOpen] = useState(false)
  const { addItem } = useCart()

  useEffect(() => {
    fetchProductsAndSides()
  }, [])

  useEffect(() => {
    calculateTotalPrice()
  }, [mealState.selectedMain, mealState.selectedSides])

  const fetchProductsAndSides = async () => {
    try {
      const response = await fetch('/api/products')
      if (!response.ok) throw new Error('Failed to fetch products')
      
      const data = await response.json()
      setProducts(data.products || [])
      setSides(data.sides || [])
    } catch (error) {
      console.error('Error fetching products:', error)
      toast.error('Failed to load menu items')
    } finally {
      setLoading(false)
    }
  }

  const calculateTotalPrice = () => {
    let total = 0
    
    if (mealState.selectedMain) {
      total += mealState.selectedMain.price * mealState.selectedMain.quantity
    }
    
    mealState.selectedSides.forEach(side => {
      total += side.price * side.quantity
    })
    
    setMealState(prev => ({ ...prev, totalPrice: total }))
  }

  const selectMainItem = (product: Product, quantity: number = 1) => {
    const mainItem: MealBuilderItem = {
      id: product.id,
      type: 'product',
      name: product.name,
      price: product.basePrice,
      quantity,
      imageUrl: product.imageUrl,
      category: product.category,
      variety: product.variety
    }
    
    setMealState(prev => ({ ...prev, selectedMain: mainItem }))
    toast.success(`Selected ${product.name}`)
  }

  const addSideItem = (side: Side, quantity: number = 1) => {
    const existingSideIndex = mealState.selectedSides.findIndex(s => s.id === side.id)
    
    if (existingSideIndex >= 0) {
      // Update existing side quantity
      const updatedSides = [...mealState.selectedSides]
      updatedSides[existingSideIndex].quantity += quantity
      setMealState(prev => ({ ...prev, selectedSides: updatedSides }))
    } else {
      // Add new side
      const sideItem: MealBuilderItem = {
        id: side.id,
        type: 'side',
        name: side.name,
        price: side.price,
        quantity,
        imageUrl: side.imageUrl,
        category: side.category
      }
      setMealState(prev => ({ 
        ...prev, 
        selectedSides: [...prev.selectedSides, sideItem] 
      }))
    }
    
    toast.success(`Added ${side.name}`)
  }

  const removeSideItem = (sideId: string) => {
    setMealState(prev => ({
      ...prev,
      selectedSides: prev.selectedSides.filter(side => side.id !== sideId)
    }))
    toast.success('Side item removed')
  }

  const updateSideQuantity = (sideId: string, quantity: number) => {
    if (quantity <= 0) {
      removeSideItem(sideId)
      return
    }
    
    setMealState(prev => ({
      ...prev,
      selectedSides: prev.selectedSides.map(side =>
        side.id === sideId ? { ...side, quantity } : side
      )
    }))
  }

  const updateMainQuantity = (quantity: number) => {
    if (quantity <= 0 || !mealState.selectedMain) return
    
    setMealState(prev => ({
      ...prev,
      selectedMain: prev.selectedMain ? { ...prev.selectedMain, quantity } : null
    }))
  }

  const addMealToCart = () => {
    if (!mealState.selectedMain) {
      toast.error('Please select a main item first')
      return
    }

    // Add main item to cart
    addItem({
      id: mealState.selectedMain.id,
      type: 'product',
      name: mealState.selectedMain.name,
      price: mealState.selectedMain.price,
      imageUrl: mealState.selectedMain.imageUrl,
      category: mealState.selectedMain.category,
      variety: mealState.selectedMain.variety,
      quantity: mealState.selectedMain.quantity
    })

    // Add side items to cart
    mealState.selectedSides.forEach(side => {
      addItem({
        id: side.id,
        type: 'side',
        name: side.name,
        price: side.price,
        imageUrl: side.imageUrl,
        category: side.category,
        quantity: side.quantity
      })
    })

    // Reset meal builder
    setMealState({
      selectedMain: null,
      selectedSides: [],
      totalPrice: 0
    })

    toast.success('Meal added to cart!')
    setIsBuilderOpen(false)
  }

  const clearMeal = () => {
    setMealState({
      selectedMain: null,
      selectedSides: [],
      totalPrice: 0
    })
    toast.success('Meal cleared')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="loading-spinner"></div>
        <span className="ml-3 text-gray-600">Loading meal builder...</span>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Meal Builder Toggle */}
      <div className="text-center">
        <button
          onClick={() => setIsBuilderOpen(!isBuilderOpen)}
          className="btn-primary text-lg px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          🍽️ Build Your Perfect South Indian Meal
        </button>
        <p className="text-gray-600 mt-2">
          Create custom combinations with traditional sides
        </p>
      </div>

      {/* Traditional Combos Preview */}
      <TraditionalCombos 
        products={products}
        sides={sides}
        onSelectCombo={(main, sides) => {
          selectMainItem(main)
          sides.forEach(side => addSideItem(side))
          setIsBuilderOpen(true)
        }}
      />

      {/* Meal Builder Interface */}
      <AnimatePresence>
        {isBuilderOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden"
          >
            <div className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white p-6">
              <h2 className="text-2xl font-bold mb-2">🍽️ Meal Builder</h2>
              <p className="text-primary-100">
                Select your main dish and customize with traditional sides
              </p>
            </div>

            <div className="p-6 space-y-8">
              {/* Step 1: Select Main Item */}
              <MainItemSelector
                products={products}
                selectedMain={mealState.selectedMain}
                onSelectMain={selectMainItem}
                onUpdateQuantity={updateMainQuantity}
              />

              {/* Step 2: Add Sides */}
              <SideItemSelector
                sides={sides}
                selectedSides={mealState.selectedSides}
                onAddSide={addSideItem}
                onRemoveSide={removeSideItem}
                onUpdateQuantity={updateSideQuantity}
              />

              {/* Meal Summary & Price */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <MealSummary
                  selectedMain={mealState.selectedMain}
                  selectedSides={mealState.selectedSides}
                  onUpdateMainQuantity={updateMainQuantity}
                  onUpdateSideQuantity={updateSideQuantity}
                  onRemoveSide={removeSideItem}
                />
                
                <PriceCalculator
                  selectedMain={mealState.selectedMain}
                  selectedSides={mealState.selectedSides}
                  totalPrice={mealState.totalPrice}
                  onAddToCart={addMealToCart}
                  onClear={clearMeal}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
