# FreshSteam - South Indian Food Platform

A specialized South Indian food ordering platform built with Next.js 14, featuring authentic dishes with customizable sides, dynamic pricing, and seamless payment processing.

## 🚀 Features

### Phase 1: MVP Foundation (In Progress)
- ✅ Project setup with Next.js 14 + TypeScript + Tailwind CSS
- ✅ Database schema with Prisma ORM (SQLite for development)
- ✅ Product catalog system (Idlis, Dosa, Rice dishes)
- ✅ Side items system (Chutneys, Curries, Vegetables)
- ✅ Shopping cart with persistent state (Zustand)
- ✅ Responsive UI components
- 🔄 Stripe payment integration (In Progress)
- 🔄 Basic admin panel (In Progress)

### Phase 2: Enhanced Features (Planned)
- 🔄 Product customization builder (Blakery-inspired)
- 🔄 Dynamic pricing system
- 🔄 Real-time price calculations
- 🔄 Enhanced product presentation

### Phase 3: Optimization & Growth (Planned)
- 🔄 Performance optimization
- 🔄 Analytics dashboard
- 🔄 Advanced admin features

## 🛠 Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: SQLite (development), PostgreSQL (production)
- **Payments**: Stripe API
- **State Management**: Zustand
- **UI Components**: Headless UI, Heroicons
- **Authentication**: NextAuth.js (admin only)

## 📦 Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```

4. Initialize the database:
   ```bash
   npx prisma generate
   npx prisma db push
   npx prisma db seed
   ```

5. Start the development server:
   ```bash
   npm run dev
   ```

## 🗄 Database Schema

### Products (Main Items)
- Multiple Idli varieties (Plain, Rava, Masala)
- Dosa (Masala Dosa)
- Rice dishes (Mixed Vegetable Rice)

### Sides
- **Chutneys**: Coconut, Peanut, Tomato
- **Curries**: Sambar, Rasam
- **Vegetables**: Mixed Vegetable Curry, Potato Curry

### Orders & Payments
- Order management with unique order numbers
- Stripe payment integration
- Order status tracking

## 🎨 Design Inspiration

- **The Blakery**: Product customization, dynamic pricing, clean UI
- **Airbnb**: Minimalist design, intuitive navigation
- **South Indian Culture**: Authentic colors, traditional meal combinations

## 📱 Features

### Customer Features
- Browse authentic South Indian dishes
- Add items to cart with quantity controls
- Guest checkout (no registration required)
- Order tracking with unique order numbers
- Traditional meal pairing suggestions

### Admin Features
- Product and pricing management
- Order management and status updates
- Sales analytics and reporting

## 🚀 Development Progress

### Week 1-2: Project Setup & Database ✅
- [x] Next.js project initialization
- [x] Database schema design
- [x] Seed data creation
- [x] Basic component structure

### Week 3-4: Product Catalog & Basic UI (In Progress)
- [x] Product display components
- [x] Shopping cart functionality
- [x] Responsive design
- [ ] API endpoints testing
- [ ] Error handling

### Week 5-6: Payment Integration (Planned)
- [ ] Stripe integration
- [ ] Order creation flow
- [ ] Payment confirmation
- [ ] Email receipts

### Week 7-8: Admin Panel & Testing (Planned)
- [ ] Admin authentication
- [ ] Product management
- [ ] Order management
- [ ] Comprehensive testing

## 🌟 Unique Features

1. **Authentic South Indian Focus**: Specialized menu with traditional dishes
2. **Cultural Context**: Traditional meal pairing suggestions
3. **Guest Checkout**: No registration required for customers
4. **Dynamic Pricing**: Admin-configurable pricing system
5. **Traditional Naming**: Authentic dish names with English descriptions

## 📄 License

This project is part of the FreshSteam development initiative.

---

**Status**: Phase 1 MVP Foundation - 60% Complete
**Next Milestone**: Complete basic product catalog and cart functionality
