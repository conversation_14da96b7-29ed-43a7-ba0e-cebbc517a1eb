// API endpoint for traditional combos
import { db } from '../../../lib/supabase'

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  try {
    switch (req.method) {
      case 'GET':
        await handleGet(req, res)
        break
      case 'POST':
        await handlePost(req, res)
        break
      default:
        res.setHeader('Allow', ['GET', 'POST'])
        res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('Combos API Error:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    })
  }
}

async function handleGet(req, res) {
  const { active } = req.query

  try {
    let combos = await db.getTraditionalCombos()

    // Apply filters
    if (active === 'false') {
      combos = combos.filter(c => !c.is_active)
    }

    // Transform data for frontend compatibility
    const transformedCombos = combos.map(combo => {
      const items = combo.combo_items || []
      
      // Calculate original total and discounted price
      const originalTotal = items.reduce((sum, item) => {
        const price = item.product?.base_price || item.side?.price || 0
        return sum + (parseFloat(price) * item.quantity)
      }, 0)
      
      const discountAmount = originalTotal * (combo.discount_percentage / 100)
      const finalPrice = originalTotal - discountAmount

      return {
        id: combo.id,
        name: combo.name,
        description: combo.description,
        culturalSignificance: combo.cultural_significance,
        discountPercentage: parseFloat(combo.discount_percentage),
        originalPrice: Math.round(originalTotal * 100) / 100,
        discountAmount: Math.round(discountAmount * 100) / 100,
        finalPrice: Math.round(finalPrice * 100) / 100,
        imageUrl: combo.image_url,
        isActive: combo.is_active,
        displayOrder: combo.display_order,
        items: items.map(item => ({
          id: item.id,
          type: item.product ? 'product' : 'side',
          productId: item.product_id,
          sideId: item.side_id,
          name: item.product?.name || item.side?.name,
          description: item.product?.description || item.side?.description,
          price: parseFloat(item.product?.base_price || item.side?.price || 0),
          quantity: item.quantity,
          imageUrl: item.product?.image_url || item.side?.image_url,
          category: item.product?.category?.name || item.side?.category,
          spiceLevel: item.product?.spice_level || item.side?.spice_level,
          dietaryInfo: item.product?.dietary_info || item.side?.dietary_info || {}
        })),
        createdAt: combo.created_at,
        updatedAt: combo.updated_at
      }
    })

    res.status(200).json({
      success: true,
      data: transformedCombos,
      count: transformedCombos.length
    })
  } catch (error) {
    throw new Error(`Failed to fetch combos: ${error.message}`)
  }
}

async function handlePost(req, res) {
  // Add combo to cart
  const { sessionId, comboId, customizations } = req.body

  if (!sessionId || !comboId) {
    return res.status(400).json({
      error: 'Session ID and combo ID are required'
    })
  }

  try {
    // Get combo details
    const combos = await db.getTraditionalCombos()
    const combo = combos.find(c => c.id === comboId)

    if (!combo) {
      return res.status(404).json({
        error: 'Combo not found'
      })
    }

    if (!combo.is_active) {
      return res.status(400).json({
        error: 'Combo is not available'
      })
    }

    // Calculate combo price
    const items = combo.combo_items || []
    const originalTotal = items.reduce((sum, item) => {
      const price = item.product?.base_price || item.side?.price || 0
      return sum + (parseFloat(price) * item.quantity)
    }, 0)
    
    const discountAmount = originalTotal * (combo.discount_percentage / 100)
    const finalPrice = originalTotal - discountAmount

    // Add combo as a single cart item with special combo metadata
    const comboCartItem = {
      product_id: null, // Combos are special items
      side_id: null,
      quantity: 1,
      unit_price: finalPrice,
      total_price: finalPrice,
      customizations: {
        type: 'combo',
        comboId: combo.id,
        comboName: combo.name,
        originalPrice: originalTotal,
        discountPercentage: combo.discount_percentage,
        discountAmount: discountAmount,
        items: items.map(item => ({
          type: item.product ? 'product' : 'side',
          id: item.product_id || item.side_id,
          name: item.product?.name || item.side?.name,
          quantity: item.quantity,
          price: parseFloat(item.product?.base_price || item.side?.price || 0)
        })),
        ...customizations
      }
    }

    const addedItem = await db.addToCart(sessionId, comboCartItem)

    res.status(201).json({
      success: true,
      data: {
        cartItem: addedItem,
        combo: {
          id: combo.id,
          name: combo.name,
          originalPrice: Math.round(originalTotal * 100) / 100,
          discountAmount: Math.round(discountAmount * 100) / 100,
          finalPrice: Math.round(finalPrice * 100) / 100,
          savings: Math.round(discountAmount * 100) / 100
        }
      },
      message: 'Combo added to cart successfully'
    })
  } catch (error) {
    throw new Error(`Failed to add combo to cart: ${error.message}`)
  }
}
