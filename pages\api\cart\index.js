// API endpoint for cart management
import { db } from '../../../lib/supabase'
import { v4 as uuidv4 } from 'uuid'

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  try {
    switch (req.method) {
      case 'GET':
        await handleGet(req, res)
        break
      case 'POST':
        await handlePost(req, res)
        break
      case 'PUT':
        await handlePut(req, res)
        break
      case 'DELETE':
        await handleDelete(req, res)
        break
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE'])
        res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('Cart API Error:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    })
  }
}

async function handleGet(req, res) {
  const { sessionId } = req.query

  if (!sessionId) {
    return res.status(400).json({
      error: 'Session ID is required'
    })
  }

  try {
    const cartSession = await db.getCartSession(sessionId)
    
    if (!cartSession) {
      return res.status(404).json({
        error: 'Cart session not found'
      })
    }

    // Calculate totals
    const items = cartSession.items || []
    const subtotal = items.reduce((sum, item) => sum + parseFloat(item.total_price), 0)
    const taxRate = 0.08 // 8% tax
    const taxAmount = subtotal * taxRate
    const total = subtotal + taxAmount

    // Transform data for frontend
    const transformedCart = {
      sessionId: cartSession.id,
      items: items.map(item => ({
        id: item.id,
        productId: item.product_id,
        sideId: item.side_id,
        productName: item.product?.name,
        sideName: item.side?.name,
        quantity: item.quantity,
        unitPrice: parseFloat(item.unit_price),
        totalPrice: parseFloat(item.total_price),
        customizations: item.customizations || {},
        imageUrl: item.product?.image_url || item.side?.image_url
      })),
      summary: {
        itemCount: items.length,
        totalQuantity: items.reduce((sum, item) => sum + item.quantity, 0),
        subtotal: Math.round(subtotal * 100) / 100,
        taxAmount: Math.round(taxAmount * 100) / 100,
        total: Math.round(total * 100) / 100
      },
      createdAt: cartSession.created_at,
      updatedAt: cartSession.updated_at
    }

    res.status(200).json({
      success: true,
      data: transformedCart
    })
  } catch (error) {
    throw new Error(`Failed to fetch cart: ${error.message}`)
  }
}

async function handlePost(req, res) {
  const { sessionId, action, item } = req.body

  try {
    if (action === 'create_session') {
      // Create new cart session
      const newSession = await db.createCartSession()
      
      res.status(201).json({
        success: true,
        data: {
          sessionId: newSession.id,
          sessionToken: newSession.session_token
        },
        message: 'Cart session created successfully'
      })
      return
    }

    if (action === 'add_item') {
      if (!sessionId || !item) {
        return res.status(400).json({
          error: 'Session ID and item data are required'
        })
      }

      // Validate required item fields
      if (!item.productId && !item.sideId) {
        return res.status(400).json({
          error: 'Either productId or sideId is required'
        })
      }

      if (!item.quantity || !item.unitPrice) {
        return res.status(400).json({
          error: 'Quantity and unitPrice are required'
        })
      }

      const cartItem = {
        product_id: item.productId || null,
        side_id: item.sideId || null,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        total_price: item.quantity * item.unitPrice,
        customizations: item.customizations || {}
      }

      const addedItem = await db.addToCart(sessionId, cartItem)

      res.status(201).json({
        success: true,
        data: addedItem,
        message: 'Item added to cart successfully'
      })
      return
    }

    res.status(400).json({
      error: 'Invalid action',
      validActions: ['create_session', 'add_item']
    })
  } catch (error) {
    throw new Error(`Failed to process cart action: ${error.message}`)
  }
}

async function handlePut(req, res) {
  const { itemId, quantity, customizations } = req.body

  if (!itemId) {
    return res.status(400).json({
      error: 'Item ID is required'
    })
  }

  try {
    const updates = {}
    
    if (quantity !== undefined) {
      updates.quantity = quantity
      // Recalculate total price if quantity changes
      const currentItem = await supabase
        .from('cart_items')
        .select('unit_price')
        .eq('id', itemId)
        .single()
      
      if (currentItem.data) {
        updates.total_price = quantity * parseFloat(currentItem.data.unit_price)
      }
    }
    
    if (customizations !== undefined) {
      updates.customizations = customizations
    }

    const updatedItem = await db.updateCartItem(itemId, updates)

    res.status(200).json({
      success: true,
      data: updatedItem,
      message: 'Cart item updated successfully'
    })
  } catch (error) {
    throw new Error(`Failed to update cart item: ${error.message}`)
  }
}

async function handleDelete(req, res) {
  const { itemId } = req.query

  if (!itemId) {
    return res.status(400).json({
      error: 'Item ID is required'
    })
  }

  try {
    await db.removeFromCart(itemId)

    res.status(200).json({
      success: true,
      message: 'Item removed from cart successfully'
    })
  } catch (error) {
    throw new Error(`Failed to remove cart item: ${error.message}`)
  }
}
