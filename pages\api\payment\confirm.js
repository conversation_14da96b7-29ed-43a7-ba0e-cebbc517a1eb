// Payment confirmation and order completion
import <PERSON><PERSON> from 'stripe'
import { db } from '../../../lib/supabase'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16'
})

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST'])
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { paymentIntentId, orderId } = req.body

    if (!paymentIntentId || !orderId) {
      return res.status(400).json({
        error: 'Payment intent ID and order ID are required'
      })
    }

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)

    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({
        error: 'Payment not completed',
        status: paymentIntent.status
      })
    }

    // Update order status
    const paymentData = {
      payment_status: 'paid',
      payment_method: paymentIntent.payment_method_types[0] || 'card',
      stripe_payment_id: paymentIntent.id
    }

    const updatedOrder = await db.updateOrderStatus(orderId, 'confirmed', paymentData)

    // Get cart session to copy items to order_items
    const cartSession = await db.getCartSession(paymentIntent.metadata.sessionId)
    
    if (cartSession && cartSession.items) {
      // Copy cart items to order_items for historical record
      for (const item of cartSession.items) {
        await supabase
          .from('order_items')
          .insert({
            order_id: orderId,
            product_id: item.product_id,
            side_id: item.side_id,
            product_name: item.product?.name || 'Combo Item',
            side_name: item.side?.name,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price,
            customizations: item.customizations
          })
      }
    }

    // Calculate estimated delivery time (30-45 minutes)
    const estimatedDelivery = new Date()
    estimatedDelivery.setMinutes(estimatedDelivery.getMinutes() + 35)

    // Update order with estimated delivery
    await supabase
      .from('orders')
      .update({
        estimated_delivery: estimatedDelivery.toISOString()
      })
      .eq('id', orderId)

    // Prepare order confirmation data
    const orderConfirmation = {
      orderId: updatedOrder.id,
      orderNumber: updatedOrder.order_number,
      status: updatedOrder.status,
      paymentStatus: updatedOrder.payment_status,
      customerEmail: updatedOrder.customer_email,
      customerPhone: updatedOrder.customer_phone,
      deliveryAddress: updatedOrder.delivery_address,
      estimatedDelivery: estimatedDelivery.toISOString(),
      specialInstructions: updatedOrder.special_instructions,
      summary: {
        subtotal: parseFloat(updatedOrder.subtotal),
        taxAmount: parseFloat(updatedOrder.tax_amount),
        deliveryFee: parseFloat(updatedOrder.delivery_fee),
        totalAmount: parseFloat(updatedOrder.total_amount)
      },
      items: cartSession?.items?.map(item => {
        const isCombo = item.customizations?.type === 'combo'
        
        if (isCombo) {
          return {
            type: 'combo',
            name: item.customizations.comboName,
            description: `Traditional combo with ${item.customizations.items.length} items`,
            quantity: item.quantity,
            unitPrice: parseFloat(item.unit_price),
            totalPrice: parseFloat(item.total_price),
            originalPrice: item.customizations.originalPrice,
            discountAmount: item.customizations.discountAmount,
            savings: item.customizations.discountAmount,
            comboItems: item.customizations.items
          }
        } else {
          return {
            type: item.product ? 'product' : 'side',
            name: item.product?.name || item.side?.name,
            description: item.product?.description || item.side?.description,
            quantity: item.quantity,
            unitPrice: parseFloat(item.unit_price),
            totalPrice: parseFloat(item.total_price),
            customizations: item.customizations || {}
          }
        }
      }) || [],
      paymentDetails: {
        paymentIntentId: paymentIntent.id,
        paymentMethod: paymentData.payment_method,
        amountPaid: paymentIntent.amount / 100,
        currency: paymentIntent.currency.toUpperCase(),
        receiptUrl: paymentIntent.charges?.data[0]?.receipt_url
      },
      createdAt: updatedOrder.created_at,
      updatedAt: updatedOrder.updated_at
    }

    res.status(200).json({
      success: true,
      data: orderConfirmation,
      message: 'Order confirmed successfully'
    })

  } catch (error) {
    console.error('Payment confirmation error:', error)
    
    if (error.type === 'StripeInvalidRequestError') {
      res.status(400).json({
        error: 'Invalid payment request',
        message: error.message
      })
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      })
    }
  }
}
