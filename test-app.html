<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FreshSteam - South Indian Food Platform</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fef7ed',
                            100: '#fdedd3',
                            200: '#fbd7a5',
                            300: '#f8bb6d',
                            400: '#f59532',
                            500: '#f37a0a',
                            600: '#e45e05',
                            700: '#bd4508',
                            800: '#96370e',
                            900: '#792f0f',
                        },
                        secondary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        .card {
            @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
        }
        .card-hover {
            @apply bg-white rounded-lg shadow-md border border-gray-200 p-6 transition-all duration-200 hover:shadow-lg hover:border-gray-300;
        }
        .btn-primary {
            @apply bg-primary-500 hover:bg-primary-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
        }
        .btn-secondary {
            @apply bg-secondary-500 hover:bg-secondary-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Mock data
        const mockProducts = [
            {
                id: 'idli-plain',
                name: 'Plain Idli',
                description: 'Soft, fluffy steamed rice cakes made from fermented rice and lentil batter. A classic South Indian breakfast staple.',
                category: 'idli',
                variety: 'plain',
                basePrice: 8.99,
                imageUrl: 'https://images.unsplash.com/photo-1589301760014-d929f3979dbc?w=400&h=300&fit=crop',
                isAvailable: true
            },
            {
                id: 'dosa-masala',
                name: 'Masala Dosa',
                description: 'Crispy golden crepe filled with spiced potato curry, served with traditional accompaniments.',
                category: 'dosa',
                variety: 'masala',
                basePrice: 12.99,
                imageUrl: 'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?w=400&h=300&fit=crop',
                isAvailable: true
            }
        ];

        const mockSides = [
            {
                id: 'coconut-chutney',
                name: 'Coconut Chutney',
                description: 'Fresh coconut ground with green chilies, ginger, and curry leaves.',
                category: 'chutney',
                price: 3.99,
                imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop',
                isAvailable: true
            },
            {
                id: 'sambar',
                name: 'Sambar',
                description: 'Traditional lentil soup with vegetables, tamarind, and aromatic spices.',
                category: 'curry',
                price: 4.99,
                imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop',
                isAvailable: true
            }
        ];

        // Simple cart store
        const useCart = () => {
            const [items, setItems] = useState([]);
            const [isOpen, setIsOpen] = useState(false);

            const addItem = (item) => {
                setItems(prev => {
                    const existing = prev.find(i => i.id === item.id);
                    if (existing) {
                        return prev.map(i => 
                            i.id === item.id 
                                ? { ...i, quantity: i.quantity + (item.quantity || 1) }
                                : i
                        );
                    }
                    return [...prev, { ...item, quantity: item.quantity || 1 }];
                });
            };

            const getTotalItems = () => items.reduce((total, item) => total + item.quantity, 0);
            const getTotalPrice = () => items.reduce((total, item) => total + (item.price * item.quantity), 0);

            return { items, addItem, getTotalItems, getTotalPrice, isOpen, toggleCart: () => setIsOpen(!isOpen) };
        };

        // Simple Product Card Component
        const ProductCard = ({ product, onAddToCart }) => (
            <div className="card-hover">
                <div className="relative h-32 bg-gray-200 rounded-lg mb-4 overflow-hidden">
                    <img 
                        src={product.imageUrl} 
                        alt={product.name}
                        className="w-full h-full object-cover"
                    />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.name}</h3>
                <p className="text-sm text-gray-600 mb-3">{product.description}</p>
                <div className="flex items-center justify-between">
                    <span className="text-xl font-bold text-primary-600">${product.basePrice.toFixed(2)}</span>
                    <button 
                        onClick={() => onAddToCart(product)}
                        className="btn-primary"
                    >
                        Add to Cart
                    </button>
                </div>
            </div>
        );

        // Simple Side Card Component
        const SideCard = ({ side, onAddToCart }) => (
            <div className="card-hover">
                <div className="relative h-24 bg-gray-200 rounded-lg mb-3 overflow-hidden">
                    <img 
                        src={side.imageUrl} 
                        alt={side.name}
                        className="w-full h-full object-cover"
                    />
                </div>
                <h4 className="text-md font-semibold text-gray-900 mb-1">{side.name}</h4>
                <p className="text-xs text-gray-600 mb-2">{side.description}</p>
                <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-secondary-600">${side.price.toFixed(2)}</span>
                    <button 
                        onClick={() => onAddToCart(side)}
                        className="btn-secondary text-sm py-1 px-3"
                    >
                        Add
                    </button>
                </div>
            </div>
        );

        // Main App Component
        const App = () => {
            const cart = useCart();

            const handleAddProduct = (product) => {
                cart.addItem({
                    id: product.id,
                    type: 'product',
                    name: product.name,
                    price: product.basePrice,
                    imageUrl: product.imageUrl
                });
                alert(`Added ${product.name} to cart!`);
            };

            const handleAddSide = (side) => {
                cart.addItem({
                    id: side.id,
                    type: 'side',
                    name: side.name,
                    price: side.price,
                    imageUrl: side.imageUrl
                });
                alert(`Added ${side.name} to cart!`);
            };

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header */}
                    <header className="bg-white shadow-sm border-b border-gray-200">
                        <div className="container mx-auto px-4 py-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="text-2xl">🍽️</div>
                                    <h1 className="text-2xl font-bold text-gray-900">FreshSteam</h1>
                                </div>
                                <button 
                                    onClick={cart.toggleCart}
                                    className="btn-primary flex items-center space-x-2"
                                >
                                    <span>🛒</span>
                                    <span>Cart ({cart.getTotalItems()})</span>
                                </button>
                            </div>
                        </div>
                    </header>

                    {/* Hero Section */}
                    <section className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-16">
                        <div className="container mx-auto px-4 text-center">
                            <h2 className="text-4xl font-bold mb-4">Authentic South Indian Cuisine</h2>
                            <p className="text-xl text-primary-100 mb-8">
                                Experience traditional flavors with modern convenience
                            </p>
                            <div className="text-6xl mb-4">🥟🥞🍚</div>
                        </div>
                    </section>

                    {/* Main Content */}
                    <main className="container mx-auto px-4 py-12">
                        {/* Products Section */}
                        <section className="mb-12">
                            <h3 className="text-2xl font-bold text-gray-900 mb-6">Main Dishes</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {mockProducts.map(product => (
                                    <ProductCard 
                                        key={product.id} 
                                        product={product} 
                                        onAddToCart={handleAddProduct}
                                    />
                                ))}
                            </div>
                        </section>

                        {/* Sides Section */}
                        <section className="mb-12">
                            <h3 className="text-2xl font-bold text-gray-900 mb-6">Traditional Sides</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                {mockSides.map(side => (
                                    <SideCard 
                                        key={side.id} 
                                        side={side} 
                                        onAddToCart={handleAddSide}
                                    />
                                ))}
                            </div>
                        </section>

                        {/* Cart Summary */}
                        {cart.items.length > 0 && (
                            <section className="bg-white rounded-lg shadow-lg p-6">
                                <h3 className="text-xl font-bold text-gray-900 mb-4">Cart Summary</h3>
                                <div className="space-y-2 mb-4">
                                    {cart.items.map(item => (
                                        <div key={item.id} className="flex justify-between">
                                            <span>{item.name} x{item.quantity}</span>
                                            <span>${(item.price * item.quantity).toFixed(2)}</span>
                                        </div>
                                    ))}
                                </div>
                                <div className="border-t pt-4">
                                    <div className="flex justify-between text-lg font-bold">
                                        <span>Total: ${cart.getTotalPrice().toFixed(2)}</span>
                                        <button className="btn-primary">Checkout</button>
                                    </div>
                                </div>
                            </section>
                        )}
                    </main>

                    {/* Footer */}
                    <footer className="bg-gray-800 text-white py-8">
                        <div className="container mx-auto px-4 text-center">
                            <p>&copy; 2024 FreshSteam. Authentic South Indian cuisine delivered fresh.</p>
                        </div>
                    </footer>
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
