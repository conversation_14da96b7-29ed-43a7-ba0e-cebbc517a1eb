// API endpoint for products
import { db } from '../../../lib/supabase'

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  try {
    switch (req.method) {
      case 'GET':
        await handleGet(req, res)
        break
      case 'POST':
        await handlePost(req, res)
        break
      case 'PUT':
        await handlePut(req, res)
        break
      case 'DELETE':
        await handleDelete(req, res)
        break
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE'])
        res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('API Error:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    })
  }
}

async function handleGet(req, res) {
  const { category, featured, available } = req.query

  try {
    let products = await db.getProducts()

    // Apply filters
    if (category) {
      products = products.filter(p => p.category?.name.toLowerCase() === category.toLowerCase())
    }
    
    if (featured === 'true') {
      products = products.filter(p => p.is_featured)
    }
    
    if (available === 'false') {
      products = products.filter(p => !p.is_available)
    }

    // Transform data for frontend compatibility
    const transformedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      culturalContext: product.cultural_context,
      category: product.category?.name || 'Unknown',
      basePrice: parseFloat(product.base_price),
      imageUrl: product.image_url,
      ingredients: product.ingredients || [],
      allergens: product.allergens || [],
      dietaryInfo: product.dietary_info || {},
      spiceLevel: product.spice_level || 1,
      prepTime: product.prep_time,
      isAvailable: product.is_available,
      isFeatured: product.is_featured,
      displayOrder: product.display_order,
      createdAt: product.created_at,
      updatedAt: product.updated_at
    }))

    res.status(200).json({
      success: true,
      data: transformedProducts,
      count: transformedProducts.length
    })
  } catch (error) {
    throw new Error(`Failed to fetch products: ${error.message}`)
  }
}

async function handlePost(req, res) {
  // Admin only - create new product
  const {
    categoryId,
    name,
    description,
    culturalContext,
    basePrice,
    imageUrl,
    ingredients,
    allergens,
    dietaryInfo,
    spiceLevel,
    prepTime,
    isFeatured
  } = req.body

  if (!name || !basePrice || !categoryId) {
    return res.status(400).json({
      error: 'Missing required fields',
      required: ['name', 'basePrice', 'categoryId']
    })
  }

  try {
    const { data, error } = await supabase
      .from('products')
      .insert({
        category_id: categoryId,
        name,
        description,
        cultural_context: culturalContext,
        base_price: basePrice,
        image_url: imageUrl,
        ingredients: ingredients || [],
        allergens: allergens || [],
        dietary_info: dietaryInfo || {},
        spice_level: spiceLevel || 1,
        prep_time: prepTime,
        is_featured: isFeatured || false,
        is_available: true
      })
      .select()
      .single()

    if (error) throw error

    res.status(201).json({
      success: true,
      data: data,
      message: 'Product created successfully'
    })
  } catch (error) {
    throw new Error(`Failed to create product: ${error.message}`)
  }
}

async function handlePut(req, res) {
  // Admin only - update product
  const { id } = req.query
  const updates = req.body

  if (!id) {
    return res.status(400).json({
      error: 'Product ID is required'
    })
  }

  try {
    const updatedProduct = await db.updateProduct(id, {
      name: updates.name,
      description: updates.description,
      cultural_context: updates.culturalContext,
      base_price: updates.basePrice,
      image_url: updates.imageUrl,
      ingredients: updates.ingredients,
      allergens: updates.allergens,
      dietary_info: updates.dietaryInfo,
      spice_level: updates.spiceLevel,
      prep_time: updates.prepTime,
      is_available: updates.isAvailable,
      is_featured: updates.isFeatured
    })

    res.status(200).json({
      success: true,
      data: updatedProduct,
      message: 'Product updated successfully'
    })
  } catch (error) {
    throw new Error(`Failed to update product: ${error.message}`)
  }
}

async function handleDelete(req, res) {
  // Admin only - soft delete product
  const { id } = req.query

  if (!id) {
    return res.status(400).json({
      error: 'Product ID is required'
    })
  }

  try {
    const updatedProduct = await db.updateProduct(id, {
      is_available: false
    })

    res.status(200).json({
      success: true,
      data: updatedProduct,
      message: 'Product deleted successfully'
    })
  } catch (error) {
    throw new Error(`Failed to delete product: ${error.message}`)
  }
}
