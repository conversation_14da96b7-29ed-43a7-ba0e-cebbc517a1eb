'use client'

import Image from 'next/image'
import { useCart } from '@/store/cart'

// Simple icon component
const PlusIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
)

interface Product {
  id: string
  name: string
  description: string
  category: string
  variety: string
  basePrice: number
  imageUrl: string
  isAvailable: boolean
}

interface ProductCardProps {
  product: Product
}

export function ProductCard({ product }: ProductCardProps) {
  const { addItem } = useCart()

  const handleAddToCart = () => {
    addItem({
      id: product.id,
      type: 'product',
      name: product.name,
      price: product.basePrice,
      imageUrl: product.imageUrl,
      category: product.category,
      variety: product.variety,
    })
  }

  return (
    <div className="card-hover overflow-hidden group">
      {/* Product Image */}
      <div className="relative h-48 bg-gray-200">
        {product.imageUrl ? (
          <Image
            src={product.imageUrl}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="h-full w-full bg-gray-200 flex items-center justify-center">
            <span className="text-gray-400">No Image</span>
          </div>
        )}
        
        {/* Availability Badge */}
        {!product.isAvailable && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium">
            Out of Stock
          </div>
        )}

        {/* Category Badge */}
        <div className="absolute top-2 right-2 bg-primary-500 text-white px-2 py-1 rounded-md text-xs font-medium capitalize">
          {product.category}
        </div>
      </div>

      {/* Product Info */}
      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {product.name}
          </h3>
          <p className="text-sm text-gray-600 line-clamp-2">
            {product.description}
          </p>
          {product.variety && (
            <p className="text-xs text-primary-600 mt-1 capitalize font-medium">
              {product.variety} variety
            </p>
          )}
        </div>

        {/* Price and Add to Cart */}
        <div className="flex items-center justify-between">
          <div className="text-xl font-bold text-gray-900">
            ${product.basePrice.toFixed(2)}
          </div>
          
          <button
            onClick={handleAddToCart}
            disabled={!product.isAvailable}
            className={`
              flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors duration-200
              ${product.isAvailable
                ? 'bg-primary-500 hover:bg-primary-600 text-white'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }
            `}
          >
            <PlusIcon className="h-4 w-4" />
            <span>{product.isAvailable ? 'Add to Cart' : 'Unavailable'}</span>
          </button>
        </div>

        {/* Traditional Pairing Suggestion */}
        {product.category === 'idli' && (
          <div className="mt-3 text-xs text-gray-500 bg-gray-50 p-2 rounded">
            💡 <strong>Traditional pairing:</strong> Best served with sambar and coconut chutney
          </div>
        )}
        {product.category === 'dosa' && (
          <div className="mt-3 text-xs text-gray-500 bg-gray-50 p-2 rounded">
            💡 <strong>Traditional pairing:</strong> Perfect with sambar and variety of chutneys
          </div>
        )}
        {product.category === 'rice' && (
          <div className="mt-3 text-xs text-gray-500 bg-gray-50 p-2 rounded">
            💡 <strong>Traditional pairing:</strong> Complements well with rasam and vegetable curry
          </div>
        )}
      </div>
    </div>
  )
}
