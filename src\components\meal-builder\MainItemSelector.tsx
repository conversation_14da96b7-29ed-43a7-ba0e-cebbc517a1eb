'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { CheckCircleIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/outline'
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid'

interface Product {
  id: string
  name: string
  description: string
  category: string
  variety: string
  basePrice: number
  imageUrl: string
  isAvailable: boolean
}

interface MealBuilderItem {
  id: string
  type: 'product' | 'side'
  name: string
  price: number
  quantity: number
  imageUrl?: string
  category?: string
  variety?: string
}

interface MainItemSelectorProps {
  products: Product[]
  selectedMain: MealBuilderItem | null
  onSelectMain: (product: Product, quantity?: number) => void
  onUpdateQuantity: (quantity: number) => void
}

export function MainItemSelector({ 
  products, 
  selectedMain, 
  onSelectMain, 
  onUpdateQuantity 
}: MainItemSelectorProps) {
  // Group products by category
  const groupedProducts = products.reduce((acc, product) => {
    if (!acc[product.category]) {
      acc[product.category] = []
    }
    acc[product.category].push(product)
    return acc
  }, {} as Record<string, Product[]>)

  const categoryOrder = ['idli', 'dosa', 'rice']
  const categoryNames = {
    idli: 'Idli Varieties',
    dosa: 'Dosa',
    rice: 'Rice Dishes'
  }

  const categoryIcons = {
    idli: '🥟',
    dosa: '🥞',
    rice: '🍚'
  }

  const categoryDescriptions = {
    idli: 'Soft, fluffy steamed rice cakes - perfect for breakfast or light meals',
    dosa: 'Crispy golden crepes with delicious fillings',
    rice: 'Aromatic rice dishes with traditional spices and vegetables'
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          Step 1: Choose Your Main Dish
        </h3>
        <p className="text-gray-600">
          Select from our authentic South Indian specialties
        </p>
      </div>

      {categoryOrder.map((category) => {
        const categoryProducts = groupedProducts[category]
        if (!categoryProducts || categoryProducts.length === 0) return null

        return (
          <motion.div
            key={category}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {/* Category Header */}
            <div className="text-center py-4 bg-gray-50 rounded-lg">
              <div className="text-3xl mb-2">
                {categoryIcons[category as keyof typeof categoryIcons]}
              </div>
              <h4 className="text-lg font-semibold text-gray-900">
                {categoryNames[category as keyof typeof categoryNames]}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {categoryDescriptions[category as keyof typeof categoryDescriptions]}
              </p>
            </div>

            {/* Products Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {categoryProducts.map((product) => {
                const isSelected = selectedMain?.id === product.id
                
                return (
                  <motion.div
                    key={product.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`
                      relative card-hover cursor-pointer transition-all duration-200
                      ${isSelected 
                        ? 'ring-2 ring-primary-500 bg-primary-50' 
                        : 'hover:shadow-lg'
                      }
                      ${!product.isAvailable ? 'opacity-50 cursor-not-allowed' : ''}
                    `}
                    onClick={() => product.isAvailable && onSelectMain(product)}
                  >
                    {/* Selection Indicator */}
                    {isSelected && (
                      <div className="absolute top-2 right-2 z-10">
                        <CheckCircleIconSolid className="h-6 w-6 text-primary-500" />
                      </div>
                    )}

                    {/* Product Image */}
                    <div className="relative h-32 bg-gray-200 rounded-t-lg overflow-hidden">
                      {product.imageUrl ? (
                        <Image
                          src={product.imageUrl}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400 text-sm">No Image</span>
                        </div>
                      )}
                      
                      {!product.isAvailable && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <span className="text-white font-medium">Out of Stock</span>
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="p-4">
                      <h5 className="font-semibold text-gray-900 mb-1">
                        {product.name}
                      </h5>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {product.description}
                      </p>
                      {product.variety && (
                        <p className="text-xs text-primary-600 mb-2 capitalize font-medium">
                          {product.variety} variety
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-gray-900">
                          ${product.basePrice.toFixed(2)}
                        </span>
                        
                        {isSelected ? (
                          <div className="flex items-center space-x-2 bg-white rounded-lg px-2 py-1 border">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                onUpdateQuantity(Math.max(1, (selectedMain?.quantity || 1) - 1))
                              }}
                              className="p-1 hover:bg-gray-100 rounded"
                            >
                              <MinusIcon className="h-4 w-4" />
                            </button>
                            <span className="font-medium min-w-[20px] text-center">
                              {selectedMain?.quantity || 1}
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                onUpdateQuantity((selectedMain?.quantity || 1) + 1)
                              }}
                              className="p-1 hover:bg-gray-100 rounded"
                            >
                              <PlusIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ) : (
                          <CheckCircleIcon className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>

                    {/* Traditional Pairing Info */}
                    {isSelected && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        className="px-4 pb-4"
                      >
                        <div className="bg-primary-50 p-3 rounded-lg text-xs">
                          <strong className="text-primary-700">💡 Traditional pairing:</strong>
                          <span className="text-primary-600 ml-1">
                            {category === 'idli' && 'Best with sambar and coconut chutney'}
                            {category === 'dosa' && 'Perfect with sambar and variety of chutneys'}
                            {category === 'rice' && 'Complements well with rasam and vegetable curry'}
                          </span>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        )
      })}

      {/* Selection Status */}
      {selectedMain && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 border border-green-200 rounded-lg p-4 text-center"
        >
          <CheckCircleIconSolid className="h-6 w-6 text-green-500 mx-auto mb-2" />
          <p className="text-green-800 font-medium">
            Selected: {selectedMain.name} (Quantity: {selectedMain.quantity})
          </p>
          <p className="text-green-600 text-sm">
            Now add your favorite sides to complete your meal!
          </p>
        </motion.div>
      )}
    </div>
  )
}
