// Stripe payment intent creation
import <PERSON><PERSON> from 'stripe'
import { db } from '../../../lib/supabase'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16'
})

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST'])
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const {
      sessionId,
      customerEmail,
      customerPhone,
      deliveryAddress,
      specialInstructions
    } = req.body

    // Validate required fields
    if (!sessionId || !customerEmail) {
      return res.status(400).json({
        error: 'Session ID and customer email are required'
      })
    }

    // Get cart details
    const cartSession = await db.getCartSession(sessionId)
    
    if (!cartSession || !cartSession.items || cartSession.items.length === 0) {
      return res.status(400).json({
        error: 'Cart is empty or session not found'
      })
    }

    // Calculate totals
    const items = cartSession.items
    const subtotal = items.reduce((sum, item) => sum + parseFloat(item.total_price), 0)
    const taxRate = 0.08 // 8% tax
    const taxAmount = subtotal * taxRate
    const deliveryFee = subtotal >= 25 ? 0 : 3.99 // Free delivery over $25
    const totalAmount = subtotal + taxAmount + deliveryFee

    // Create Stripe payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Convert to cents
      currency: 'usd',
      customer_email: customerEmail,
      metadata: {
        sessionId: sessionId,
        customerEmail: customerEmail,
        customerPhone: customerPhone || '',
        itemCount: items.length.toString(),
        subtotal: subtotal.toFixed(2),
        taxAmount: taxAmount.toFixed(2),
        deliveryFee: deliveryFee.toFixed(2),
        totalAmount: totalAmount.toFixed(2)
      },
      description: `FreshSteam Order - ${items.length} items`,
      receipt_email: customerEmail,
      shipping: deliveryAddress ? {
        name: customerEmail,
        address: {
          line1: deliveryAddress.street,
          line2: deliveryAddress.apartment || '',
          city: deliveryAddress.city,
          state: deliveryAddress.state,
          postal_code: deliveryAddress.zipCode,
          country: 'US'
        }
      } : undefined
    })

    // Create order record
    const orderData = {
      cart_session_id: sessionId,
      customer_email: customerEmail,
      customer_phone: customerPhone,
      delivery_address: deliveryAddress,
      subtotal: subtotal,
      tax_amount: taxAmount,
      delivery_fee: deliveryFee,
      total_amount: totalAmount,
      payment_intent_id: paymentIntent.id,
      special_instructions: specialInstructions
    }

    const order = await db.createOrder(orderData)

    // Prepare line items for Stripe (for receipt)
    const lineItems = items.map(item => {
      const isCombo = item.customizations?.type === 'combo'
      
      if (isCombo) {
        return {
          name: `${item.customizations.comboName} (Traditional Combo)`,
          description: `${item.customizations.items.length} items with ${item.customizations.discountPercentage}% discount`,
          amount: Math.round(parseFloat(item.total_price) * 100),
          quantity: item.quantity
        }
      } else {
        const name = item.product?.name || item.side?.name || 'Unknown Item'
        return {
          name: name,
          description: item.customizations?.notes || '',
          amount: Math.round(parseFloat(item.unit_price) * 100),
          quantity: item.quantity
        }
      }
    })

    res.status(200).json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        orderId: order.id,
        orderNumber: order.order_number,
        amount: totalAmount,
        currency: 'usd',
        lineItems: lineItems,
        summary: {
          subtotal: Math.round(subtotal * 100) / 100,
          taxAmount: Math.round(taxAmount * 100) / 100,
          deliveryFee: Math.round(deliveryFee * 100) / 100,
          totalAmount: Math.round(totalAmount * 100) / 100,
          itemCount: items.length,
          totalQuantity: items.reduce((sum, item) => sum + item.quantity, 0)
        }
      },
      message: 'Payment intent created successfully'
    })

  } catch (error) {
    console.error('Payment intent creation error:', error)
    
    if (error.type === 'StripeCardError') {
      res.status(400).json({
        error: 'Payment processing error',
        message: error.message
      })
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      })
    }
  }
}
