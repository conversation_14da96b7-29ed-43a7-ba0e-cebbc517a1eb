# FreshSteam Platform - Quick Reference Guide

## 🚀 Tech Stack Summary
- **Frontend:** Next.js 14+ + TypeScript + Tailwind CSS
- **Backend:** Node.js + PostgreSQL + Prisma ORM
- **Payments:** Stripe API with webhooks
- **Auth:** NextAuth.js (admin only)
- **Deployment:** Vercel + managed PostgreSQL
- **Images:** Cloudinary or AWS S3

## 📊 Database Schema (Key Tables)
```sql
products (id, name, category, variety, base_price, image_url, is_available)
sides (id, name, category, price, image_url, is_available)  
orders (id, order_number, customer_info, total_amount, status, payment_status)
order_items (order_id, product_id, side_id, quantity, unit_price)
payments (order_id, stripe_payment_intent_id, amount, status)
admin_users (id, email, password_hash, name, role)
```

## 🛠 API Endpoints Structure
```
/api/products          - GET (public product catalog)
/api/orders           - POST (create order), GET /[orderNumber] (status)
/api/payments         - POST /create-intent, POST /webhook
/api/admin/products   - CRUD operations (auth required)
/api/admin/orders     - Order management (auth required)
/api/admin/analytics  - Business metrics (auth required)
```

## 🍽 Product Catalog Structure
**Main Items:**
- Multiple Idli varieties (Plain, Rava, Masala)
- One Dosa type (Masala Dosa)
- One Rice variety (Mixed Vegetable Rice)

**Sides:**
- Chutneys: Coconut, Peanut, Tomato
- Curries: Sambar, Rasam
- Vegetables: Seasonal vegetable sides

## 🎯 Core Features Priority
1. **Product Customization Builder** - Interactive main + sides selector
2. **Dynamic Pricing** - Real-time calculations, separate main/side pricing
3. **Shopping Cart** - Persistent state, guest checkout
4. **Stripe Integration** - Secure payments, unique order IDs
5. **Admin Panel** - Price management, order tracking

## 📱 UI/UX Requirements
- Mobile-first responsive design
- Blakery-inspired meal builder interface
- Airbnb-style clean, professional layout
- High-quality food photography
- Cultural context with authentic naming
- Touch-friendly interactions for mobile ordering

## ⏱ Development Timeline
- **Phase 1 MVP:** 6-8 weeks (Basic ordering system)
- **Phase 2 Enhanced:** 4-6 weeks (Customization features)
- **Phase 3 Optimization:** 3-4 weeks (Performance & analytics)
- **Total:** 13-18 weeks (3-4.5 months)

## 💰 Cost & ROI Summary
- **Development Cost:** $35,000 - $60,000
- **Break-even:** 6-12 months
- **Revenue Potential:** $108,000 - $450,000 annually
- **Target AOV:** $20+ per order

## 🔒 Security & Performance
- HTTPS everywhere, PCI compliance via Stripe
- Page load < 3 seconds, 99.5% uptime target
- Input validation, secure admin authentication
- Auto-scaling cloud infrastructure

## 📈 Success Metrics
- **Conversion Rate:** > 3%
- **Payment Success:** > 98%
- **Mobile Usage:** > 60%
- **Customer Retention:** > 40% repeat orders
- **Revenue Growth:** 15%+ month-over-month

## 🎨 Design Inspiration Elements
**From Blakery Analysis:**
- Interactive "Build-A-Box" → Meal Builder
- Real-time pricing display
- High-quality product imagery
- Clean navigation with prominent CTAs
- Mobile-optimized food ordering experience

**From Airbnb:**
- Clean, minimalist interface
- Intuitive user flows
- Professional typography and spacing
- Consistent design system

## 🚦 Getting Started Checklist
- [ ] Set up Next.js 14+ project with TypeScript
- [ ] Configure PostgreSQL + Prisma ORM
- [ ] Create Stripe test account
- [ ] Design database schema
- [ ] Implement product catalog API
- [ ] Build responsive UI components
- [ ] Integrate payment processing
- [ ] Create admin authentication
- [ ] Set up deployment pipeline
- [ ] Conduct testing and optimization

## 📞 Key User Stories
**Customer:** "I want to build custom South Indian meals with authentic sides and see real-time pricing"
**Admin:** "I want to update prices dynamically and track orders efficiently"

## 🎯 Competitive Advantages
- Specialized South Indian focus vs generic food delivery
- Authentic meal customization with cultural context
- Direct customer relationship (no third-party fees)
- Premium positioning through specialization
- Blakery-inspired user experience differentiation

---
*Reference: Full development prompt in `freshsteam-development-prompt.md`*
*Feasibility analysis: `freshsteam-feasibility-report.html`*
