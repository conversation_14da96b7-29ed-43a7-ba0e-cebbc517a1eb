// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String   // 'idli', 'dosa', 'rice'
  variety     String?  // 'plain', 'rava', 'masala', etc.
  basePrice   Decimal  @map("base_price")
  imageUrl    String?  @map("image_url")
  isAvailable Boolean  @default(true) @map("is_available")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  orderItems OrderItem[]

  @@map("products")
}

model Side {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String   // 'chutney', 'curry', 'vegetable'
  price       Decimal
  imageUrl    String?  @map("image_url")
  isAvailable Boolean  @default(true) @map("is_available")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  orderItems OrderItem[]

  @@map("sides")
}

model Order {
  id                    String   @id @default(cuid())
  orderNumber           String   @unique @map("order_number")
  customerName          String   @map("customer_name")
  customerEmail         String   @map("customer_email")
  customerPhone         String?  @map("customer_phone")
  totalAmount           Decimal  @map("total_amount")
  status                String   @default("pending") // pending, confirmed, preparing, ready, completed, cancelled
  paymentStatus         String   @default("pending") @map("payment_status") // pending, paid, failed, refunded
  stripePaymentIntentId String?  @map("stripe_payment_intent_id")
  notes                 String?
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  orderItems OrderItem[]
  payments   Payment[]

  @@map("orders")
}

model OrderItem {
  id         String  @id @default(cuid())
  orderId    String  @map("order_id")
  productId  String? @map("product_id")
  sideId     String? @map("side_id")
  quantity   Int     @default(1)
  unitPrice  Decimal @map("unit_price")
  totalPrice Decimal @map("total_price")
  createdAt  DateTime @default(now()) @map("created_at")

  order   Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product? @relation(fields: [productId], references: [id])
  side    Side?    @relation(fields: [sideId], references: [id])

  @@map("order_items")
}

model Payment {
  id                    String   @id @default(cuid())
  orderId               String   @map("order_id")
  stripePaymentIntentId String   @unique @map("stripe_payment_intent_id")
  amount                Decimal
  currency              String   @default("usd")
  status                String   // succeeded, failed, canceled, processing
  paymentMethod         String?  @map("payment_method")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payments")
}

model AdminUser {
  id           String   @id @default(cuid())
  email        String   @unique
  passwordHash String   @map("password_hash")
  name         String
  role         String   @default("admin")
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")

  @@map("admin_users")
}
