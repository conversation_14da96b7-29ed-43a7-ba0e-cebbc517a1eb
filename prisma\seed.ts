import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      name: 'FreshSteam Admin',
      role: 'admin',
    },
  })

  console.log('👤 Created admin user:', admin.email)

  // Create main products (idlis, dosa, rice)
  const products = [
    // Idli varieties
    {
      name: 'Plain Idli',
      description: 'Soft, fluffy steamed rice cakes - the classic South Indian breakfast',
      category: 'idli',
      variety: 'plain',
      basePrice: 8.99,
      imageUrl: 'https://images.unsplash.com/photo-1589301760014-d929f3979dbc?w=500',
    },
    {
      name: '<PERSON><PERSON> Idli',
      description: 'Semolina-based idlis with vegetables and aromatic spices',
      category: 'idli',
      variety: 'rava',
      basePrice: 10.99,
      imageUrl: 'https://images.unsplash.com/photo-1589301760014-d929f3979dbc?w=500',
    },
    {
      name: 'Masala Idli',
      description: 'Spiced idlis with onions, chilies, and traditional South Indian flavors',
      category: 'idli',
      variety: 'masala',
      basePrice: 11.99,
      imageUrl: 'https://images.unsplash.com/photo-1589301760014-d929f3979dbc?w=500',
    },
    // Dosa
    {
      name: 'Masala Dosa',
      description: 'Crispy golden crepe filled with spiced potato curry - a South Indian favorite',
      category: 'dosa',
      variety: 'masala',
      basePrice: 14.99,
      imageUrl: 'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?w=500',
    },
    // Rice
    {
      name: 'Mixed Vegetable Rice',
      description: 'Aromatic basmati rice with seasonal vegetables and traditional spices',
      category: 'rice',
      variety: 'mixed',
      basePrice: 12.99,
      imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    },
  ]

  for (const product of products) {
    await prisma.product.upsert({
      where: { name: product.name },
      update: {},
      create: product,
    })
  }

  console.log('🍽️ Created main products')

  // Create side items
  const sides = [
    // Chutneys
    {
      name: 'Coconut Chutney',
      description: 'Fresh coconut chutney with curry leaves and green chilies',
      category: 'chutney',
      price: 3.99,
      imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    },
    {
      name: 'Peanut Chutney',
      description: 'Roasted peanut chutney with tamarind and aromatic spices',
      category: 'chutney',
      price: 4.49,
      imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    },
    {
      name: 'Tomato Chutney',
      description: 'Tangy tomato chutney with onions and traditional South Indian spices',
      category: 'chutney',
      price: 4.29,
      imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    },
    // Curries
    {
      name: 'Sambar',
      description: 'Traditional lentil curry with vegetables and tamarind',
      category: 'curry',
      price: 5.99,
      imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    },
    {
      name: 'Rasam',
      description: 'Tangy tamarind soup with tomatoes, spices, and curry leaves',
      category: 'curry',
      price: 5.49,
      imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    },
    // Vegetables
    {
      name: 'Mixed Vegetable Curry',
      description: 'Seasonal vegetables cooked in coconut-based curry',
      category: 'vegetable',
      price: 6.99,
      imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    },
    {
      name: 'Potato Curry',
      description: 'Spiced potato curry with onions and traditional South Indian flavors',
      category: 'vegetable',
      price: 5.99,
      imageUrl: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    },
  ]

  for (const side of sides) {
    await prisma.side.upsert({
      where: { name: side.name },
      update: {},
      create: side,
    })
  }

  console.log('🥄 Created side items')
  console.log('✅ Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
