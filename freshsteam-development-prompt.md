# FreshSteam South Indian Food Platform - Comprehensive Development Prompt

## Project Overview
Build a specialized South Indian food ordering platform with customizable meal combinations, dynamic pricing, and seamless payment processing. The platform should provide an authentic, user-friendly experience for ordering traditional South Indian dishes with customizable sides.

**Reference Documents:**
- Feasibility Analysis: `freshsteam-feasibility-report.html`
- Design Inspiration: The Blakery website analysis (`blakery-analysis-report.html`)

## Technical Stack Requirements

### Frontend
- **Framework:** Next.js 14+ with App Router
- **Language:** TypeScript for type safety
- **Styling:** Tailwind CSS with Headless UI components
- **State Management:** Zustand or React Context for cart state
- **Mobile:** Progressive Web App (PWA) capabilities
- **Image Optimization:** Next.js Image component with lazy loading

### Backend
- **Runtime:** Node.js with Next.js API routes
- **Database:** PostgreSQL with connection pooling
- **ORM:** Prisma ORM for type-safe database operations
- **Payment Processing:** Stripe API with webhook handling
- **File Storage:** Cloudinary or AWS S3 for product images
- **Environment:** Environment variables for configuration

### Development Tools
- **Package Manager:** npm or yarn
- **Code Quality:** ESLint, Prettier, <PERSON>sky for git hooks
- **Testing:** Jest and React Testing Library
- **Deployment:** Vercel or similar with auto-scaling

## Database Schema Design

### Core Tables Structure

```sql
-- Products table (main items)
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL, -- 'idli', 'dosa', 'rice'
  variety VARCHAR(100), -- 'plain', 'rava', 'masala', etc.
  base_price DECIMAL(10,2) NOT NULL,
  image_url VARCHAR(500),
  is_available BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sides table (chutneys, sambar, etc.)
CREATE TABLE sides (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL, -- 'chutney', 'curry', 'vegetable'
  price DECIMAL(10,2) NOT NULL,
  image_url VARCHAR(500),
  is_available BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Orders table
CREATE TABLE orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_number VARCHAR(20) UNIQUE NOT NULL, -- Human-readable order ID
  customer_name VARCHAR(255) NOT NULL,
  customer_email VARCHAR(255) NOT NULL,
  customer_phone VARCHAR(20),
  total_amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(50) DEFAULT 'pending', -- pending, confirmed, preparing, ready, completed, cancelled
  payment_status VARCHAR(50) DEFAULT 'pending', -- pending, paid, failed, refunded
  stripe_payment_intent_id VARCHAR(255),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Order items (junction table for orders and products/sides)
CREATE TABLE order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  side_id UUID REFERENCES sides(id),
  quantity INTEGER NOT NULL DEFAULT 1,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payments table (for Stripe tracking)
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  stripe_payment_intent_id VARCHAR(255) UNIQUE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  status VARCHAR(50) NOT NULL, -- succeeded, failed, canceled, processing
  payment_method VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admin users table (for admin authentication)
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(50) DEFAULT 'admin',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Structure Requirements

### Public APIs (No Authentication Required)

#### `/api/products` - Product Catalog
```typescript
// GET /api/products - Get all available products with sides
interface ProductsResponse {
  products: {
    id: string;
    name: string;
    description: string;
    category: 'idli' | 'dosa' | 'rice';
    variety: string;
    basePrice: number;
    imageUrl: string;
    isAvailable: boolean;
  }[];
  sides: {
    id: string;
    name: string;
    description: string;
    category: 'chutney' | 'curry' | 'vegetable';
    price: number;
    imageUrl: string;
    isAvailable: boolean;
  }[];
}
```

#### `/api/orders` - Order Management
```typescript
// POST /api/orders - Create new order
interface CreateOrderRequest {
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  items: {
    productId?: string;
    sideId?: string;
    quantity: number;
  }[];
  notes?: string;
}

// GET /api/orders/[orderNumber] - Get order status
interface OrderStatusResponse {
  orderNumber: string;
  status: string;
  totalAmount: number;
  items: OrderItem[];
  createdAt: string;
}
```

#### `/api/payments` - Payment Processing
```typescript
// POST /api/payments/create-intent - Create Stripe payment intent
interface CreatePaymentIntentRequest {
  orderId: string;
  amount: number;
}

// POST /api/payments/webhook - Stripe webhook handler
// Handle payment confirmations and update order status
```

### Admin APIs (Authentication Required)

#### `/api/admin/products` - Product Management
```typescript
// GET, POST, PUT, DELETE operations for products and sides
// Price updates, availability toggles, product creation/editing
```

#### `/api/admin/orders` - Order Management
```typescript
// GET /api/admin/orders - List all orders with filtering
// PUT /api/admin/orders/[id]/status - Update order status
```

#### `/api/admin/analytics` - Business Analytics
```typescript
// GET /api/admin/analytics/sales - Sales reports
// GET /api/admin/analytics/popular-items - Popular products/sides
```

## Feature Implementation Requirements

### 1. Product Customization Builder (Blakery-Inspired)

**User Story:** As a customer, I want to build custom meals by selecting main items and adding multiple sides, so I can create my preferred South Indian meal combination.

**Technical Requirements:**
- Interactive meal builder interface with drag-and-drop or click-to-add functionality
- Real-time price calculation as items are added/removed
- Visual representation of selected items with quantities
- Validation for minimum/maximum quantities
- Save/load cart state in localStorage for persistence

**Implementation Details:**
```typescript
interface MealBuilder {
  selectedMain: {
    productId: string;
    quantity: number;
    price: number;
  } | null;
  selectedSides: {
    sideId: string;
    quantity: number;
    price: number;
  }[];
  totalPrice: number;
}

// Components needed:
// - MealBuilderContainer
// - ProductSelector
// - SideSelector
// - PriceCalculator
// - CartSummary
```

### 2. Dynamic Pricing System

**User Story:** As a customer, I want to see real-time price updates as I customize my order, and as an admin, I want to update prices dynamically without code changes.

**Technical Requirements:**
- Real-time price calculation with quantity discounts
- Separate pricing for main items and sides
- Admin interface for price management
- Price history tracking for analytics
- Combo offer support (future enhancement)

**Implementation Details:**
```typescript
interface PricingEngine {
  calculateItemPrice(itemId: string, quantity: number): number;
  calculateTotalPrice(items: CartItem[]): number;
  applyDiscounts(subtotal: number, discountRules: DiscountRule[]): number;
}

// Admin price management interface
interface PriceUpdateRequest {
  itemId: string;
  newPrice: number;
  effectiveDate?: Date;
}
```

### 3. Shopping Cart Functionality

**User Story:** As a customer, I want my cart to persist across browser sessions and provide easy quantity adjustments, so I can complete my order conveniently.

**Technical Requirements:**
- Persistent cart state using localStorage and database sync
- Add/remove/update quantity operations
- Cart validation before checkout
- Guest checkout flow (no user registration required)
- Cart abandonment recovery (email capture)

### 4. Stripe Payment Integration

**User Story:** As a customer, I want secure payment processing with order confirmation, and as a business owner, I want reliable payment tracking with unique order IDs.

**Technical Requirements:**
- Stripe Payment Intents API integration
- Webhook handling for payment confirmations
- Unique order number generation (e.g., FS-2025-001234)
- Payment failure handling and retry mechanisms
- Receipt generation and email delivery

**Implementation Details:**
```typescript
// Payment flow
const createPaymentIntent = async (orderId: string, amount: number) => {
  const paymentIntent = await stripe.paymentIntents.create({
    amount: amount * 100, // Convert to cents
    currency: 'usd',
    metadata: { orderId },
  });
  return paymentIntent;
};

// Webhook handler
const handleStripeWebhook = async (event: Stripe.Event) => {
  switch (event.type) {
    case 'payment_intent.succeeded':
      await updateOrderStatus(event.data.object.metadata.orderId, 'paid');
      break;
    case 'payment_intent.payment_failed':
      await updateOrderStatus(event.data.object.metadata.orderId, 'payment_failed');
      break;
  }
};
```

### 5. Admin Panel

**User Story:** As an admin, I want to manage product prices, view orders, and track business performance through an intuitive dashboard.

**Technical Requirements:**
- NextAuth.js for admin authentication
- Dashboard with key metrics (daily sales, popular items, order status)
- Product and pricing management interface
- Order management with status updates
- Basic analytics and reporting

**Admin Interface Components:**
- Login/logout functionality
- Product catalog management
- Price update interface
- Order queue with status management
- Sales analytics dashboard

## South Indian Food Context Implementation

### Product Catalog Structure

**Main Items (Products):**
```typescript
const mainItems = [
  // Idli Varieties
  { name: "Plain Idli", category: "idli", variety: "plain", description: "Soft, fluffy steamed rice cakes" },
  { name: "Rava Idli", category: "idli", variety: "rava", description: "Semolina-based idlis with vegetables" },
  { name: "Masala Idli", category: "idli", variety: "masala", description: "Spiced idlis with onions and chilies" },
  
  // Dosa
  { name: "Masala Dosa", category: "dosa", variety: "masala", description: "Crispy crepe with spiced potato filling" },
  
  // Rice
  { name: "Mixed Vegetable Rice", category: "rice", variety: "mixed", description: "Aromatic rice with seasonal vegetables" }
];
```

**Side Items:**
```typescript
const sideItems = [
  { name: "Coconut Chutney", category: "chutney", description: "Fresh coconut chutney with curry leaves" },
  { name: "Peanut Chutney", category: "chutney", description: "Roasted peanut chutney with tamarind" },
  { name: "Tomato Chutney", category: "chutney", description: "Tangy tomato chutney with spices" },
  { name: "Sambar", category: "curry", description: "Traditional lentil curry with vegetables" },
  { name: "Rasam", category: "curry", description: "Tangy tamarind soup with spices" },
  { name: "Vegetable Curry", category: "vegetable", description: "Seasonal vegetable curry" }
];
```

### Cultural Context Features
- Traditional meal combination suggestions (e.g., "Idli + Sambar + Coconut Chutney")
- Authentic Tamil/Telugu names with English descriptions
- Cultural notes about traditional serving methods
- Dietary information (vegetarian, vegan, gluten-free indicators)

## UI/UX Design Requirements (Blakery & Airbnb Inspired)

### Design Principles
- **Clean, minimalist interface** with focus on food imagery
- **Mobile-first responsive design** optimized for touch interactions
- **Warm color palette** reflecting South Indian culture (oranges, greens, earth tones)
- **High-quality food photography** with consistent styling
- **Intuitive navigation** with clear call-to-action buttons

### Key UI Components
```typescript
// Component structure
components/
├── layout/
│   ├── Header.tsx
│   ├── Footer.tsx
│   └── Navigation.tsx
├── product/
│   ├── ProductCard.tsx
│   ├── ProductGrid.tsx
│   └── ProductDetails.tsx
├── cart/
│   ├── CartSidebar.tsx
│   ├── CartItem.tsx
│   └── CartSummary.tsx
├── checkout/
│   ├── CheckoutForm.tsx
│   ├── PaymentForm.tsx
│   └── OrderConfirmation.tsx
├── admin/
│   ├── AdminDashboard.tsx
│   ├── ProductManager.tsx
│   └── OrderManager.tsx
└── ui/
    ├── Button.tsx
    ├── Input.tsx
    ├── Modal.tsx
    └── LoadingSpinner.tsx
```

### Responsive Design Requirements
- **Mobile (320px-768px):** Single column layout, touch-friendly buttons, swipe gestures
- **Tablet (768px-1024px):** Two-column layout, optimized for portrait/landscape
- **Desktop (1024px+):** Multi-column layout with sidebar cart, hover effects

## Development Phases

### Phase 1: MVP Foundation (6-8 weeks)
**Priority: Core functionality for basic ordering**

**Week 1-2: Project Setup & Database**
- Initialize Next.js project with TypeScript and Tailwind CSS
- Set up PostgreSQL database with Prisma ORM
- Implement database schema and seed data
- Set up development environment and deployment pipeline

**Week 3-4: Product Catalog & Basic UI**
- Create product and side item display components
- Implement responsive grid layout for products
- Add basic shopping cart functionality
- Create checkout form with customer information

**Week 5-6: Payment Integration**
- Integrate Stripe payment processing
- Implement webhook handling for payment confirmations
- Create order confirmation and receipt system
- Add order status tracking

**Week 7-8: Admin Panel & Testing**
- Build basic admin authentication
- Create admin dashboard for order management
- Implement price update functionality
- Comprehensive testing and bug fixes

**MVP Deliverables:**
- Functional product catalog with main items and sides
- Working shopping cart and checkout process
- Secure Stripe payment integration
- Basic admin panel for order and price management
- Mobile-responsive design
- Order tracking system

### Phase 2: Enhanced Features (4-6 weeks)
**Priority: Blakery-inspired customization and user experience**

**Week 9-10: Product Customization Builder**
- Implement interactive meal builder interface
- Add drag-and-drop or click-to-add functionality
- Create visual meal composition display
- Add quantity controls with real-time updates

**Week 11-12: Dynamic Pricing & Promotions**
- Build advanced pricing engine with quantity discounts
- Implement combo offer system
- Add promotional code functionality
- Create pricing analytics for admin

**Week 13-14: Enhanced UI/UX**
- Improve product photography and descriptions
- Add cultural context and meal suggestions
- Implement advanced filtering and search
- Optimize mobile user experience

**Phase 2 Deliverables:**
- Interactive meal customization builder
- Dynamic pricing with real-time calculations
- Enhanced product presentation with cultural context
- Improved admin analytics and reporting
- Advanced mobile optimizations

### Phase 3: Optimization & Growth (3-4 weeks)
**Priority: Performance, analytics, and business features**

**Week 15-16: Performance Optimization**
- Implement image optimization and lazy loading
- Add caching strategies for better performance
- Optimize database queries and API responses
- Implement SEO optimizations

**Week 17-18: Advanced Features**
- Add comprehensive analytics dashboard
- Implement inventory management system
- Create customer feedback and rating system
- Add email marketing integration

**Phase 3 Deliverables:**
- Optimized performance with fast loading times
- Comprehensive analytics and reporting
- Advanced admin features for business management
- Customer engagement features
- Production-ready deployment

## User Stories & Acceptance Criteria

### Customer User Stories

**Epic: Product Discovery & Selection**
```
As a customer interested in South Indian food,
I want to browse authentic dishes with clear descriptions and images,
So that I can understand what I'm ordering and make informed choices.

Acceptance Criteria:
- Product catalog displays main items (idlis, dosa, rice) with high-quality images
- Each item has authentic name, English description, and cultural context
- Sides are clearly categorized (chutneys, curries, vegetables)
- Dietary information is clearly marked (vegetarian, vegan, spicy level)
- Mobile-responsive grid layout works on all devices
```

**Epic: Meal Customization**
```
As a customer familiar with South Indian meals,
I want to build custom meal combinations with multiple sides,
So that I can recreate authentic dining experiences at home.

Acceptance Criteria:
- Interactive meal builder allows selection of 1 main + multiple sides
- Real-time price calculation updates as items are added/removed
- Visual representation shows selected items with quantities
- Traditional meal combinations are suggested
- Cart persists across browser sessions
```

**Epic: Checkout & Payment**
```
As a customer ready to order,
I want a simple, secure checkout process without creating an account,
So that I can complete my purchase quickly and safely.

Acceptance Criteria:
- Guest checkout requires only essential information (name, email, phone)
- Stripe payment integration handles cards securely
- Order confirmation provides unique order number
- Email receipt is sent automatically
- Order status can be tracked using order number
```

### Admin User Stories

**Epic: Product & Price Management**
```
As a restaurant admin,
I want to update prices and manage product availability in real-time,
So that I can respond to market changes and inventory levels.

Acceptance Criteria:
- Admin can log in securely to management dashboard
- Product prices can be updated with immediate effect
- Product availability can be toggled on/off
- Price change history is tracked for analytics
- Changes are reflected immediately on customer-facing site
```

**Epic: Order Management**
```
As a restaurant admin,
I want to view and manage incoming orders efficiently,
So that I can fulfill orders accurately and on time.

Acceptance Criteria:
- Dashboard shows all orders with status indicators
- Orders can be filtered by status, date, or customer
- Order status can be updated (confirmed, preparing, ready, completed)
- Customer contact information is easily accessible
- Order details show complete item breakdown with quantities
```

## Technical Implementation Guidelines

### Code Quality Standards
- **TypeScript:** Strict mode enabled, comprehensive type definitions
- **ESLint & Prettier:** Consistent code formatting and linting
- **Testing:** Unit tests for utilities, integration tests for API endpoints
- **Documentation:** JSDoc comments for complex functions
- **Git Workflow:** Feature branches with pull request reviews

### Performance Requirements
- **Page Load Speed:** < 3 seconds on 3G mobile connection
- **Core Web Vitals:** LCP < 2.5s, FID < 100ms, CLS < 0.1
- **Image Optimization:** WebP format with multiple sizes
- **Database Performance:** Query optimization with proper indexing
- **Caching:** Redis for session data, CDN for static assets

### Security Requirements
- **Payment Security:** PCI DSS compliance through Stripe
- **Data Protection:** HTTPS everywhere, secure headers
- **Input Validation:** Server-side validation for all user inputs
- **Admin Security:** Strong password requirements, session management
- **Environment Variables:** Secure storage of API keys and secrets

### Deployment & DevOps
- **Hosting:** Vercel or similar with auto-scaling
- **Database:** Managed PostgreSQL (AWS RDS, Supabase, or PlanetScale)
- **Monitoring:** Error tracking with Sentry, performance monitoring
- **Backup:** Automated database backups with point-in-time recovery
- **CI/CD:** Automated testing and deployment pipeline

## Success Metrics & KPIs

### Technical Performance Metrics
- **Uptime:** > 99.5% availability
- **Response Time:** API responses < 500ms average
- **Payment Success Rate:** > 98% successful transactions
- **Mobile Performance:** Core Web Vitals in "Good" range
- **Error Rate:** < 1% application errors

### Business Performance Metrics
- **Conversion Rate:** > 3% visitors to customers
- **Average Order Value:** Target $20+ per order
- **Customer Retention:** > 40% repeat orders within 3 months
- **Order Frequency:** 2+ orders per customer per month
- **Revenue Growth:** 15%+ month-over-month after launch

### User Experience Metrics
- **Cart Abandonment Rate:** < 70% (industry average is 70-80%)
- **Checkout Completion Rate:** > 85% of initiated checkouts
- **Mobile Usage:** > 60% of orders from mobile devices
- **Customer Satisfaction:** > 4.5/5 average rating
- **Support Tickets:** < 5% of orders require support

## Risk Mitigation Strategies

### Technical Risks
- **Payment Failures:** Implement retry mechanisms and clear error messages
- **Performance Issues:** Load testing before launch, monitoring alerts
- **Security Vulnerabilities:** Regular security audits, dependency updates
- **Data Loss:** Automated backups, disaster recovery procedures

### Business Risks
- **Low Adoption:** Soft launch with limited marketing, gather feedback
- **Competition:** Focus on authentic South Indian specialization
- **Operational Scaling:** Cloud-based infrastructure with auto-scaling
- **Customer Support:** Clear FAQ, order tracking, responsive support

## Getting Started Checklist

### Pre-Development Setup
- [ ] Set up development environment (Node.js, PostgreSQL, VS Code)
- [ ] Create project repository with proper branching strategy
- [ ] Set up Stripe test account and obtain API keys
- [ ] Design database schema and create migration files
- [ ] Prepare high-quality product images and descriptions

### Phase 1 Development Tasks
- [ ] Initialize Next.js project with TypeScript and Tailwind CSS
- [ ] Set up Prisma ORM with PostgreSQL connection
- [ ] Implement authentication system for admin users
- [ ] Create product catalog API endpoints
- [ ] Build responsive product display components
- [ ] Implement shopping cart functionality
- [ ] Integrate Stripe payment processing
- [ ] Create admin dashboard for basic management
- [ ] Set up deployment pipeline and hosting
- [ ] Conduct thorough testing and bug fixes

### Launch Preparation
- [ ] Load testing with realistic traffic scenarios
- [ ] Security audit and penetration testing
- [ ] Content creation (product descriptions, images, cultural context)
- [ ] Staff training on admin panel usage
- [ ] Customer support documentation and procedures
- [ ] Marketing materials and launch strategy
- [ ] Analytics setup and conversion tracking
- [ ] Backup and disaster recovery procedures

---

This comprehensive development prompt provides all the technical specifications, user stories, and implementation details needed to build the FreshSteam South Indian Food Platform. The prompt is structured to be used by development teams or AI assistants, with clear phases, acceptance criteria, and success metrics based on the feasibility analysis.
