// Admin dashboard data endpoint
import jwt from 'jsonwebtoken'
import { supabase } from '../../../lib/supabase'

// Middleware to verify admin token
function verifyAdminToken(req) {
  const authHeader = req.headers.authorization
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('No valid authorization token provided')
  }

  const token = authHeader.substring(7)
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret')
    return decoded
  } catch (error) {
    throw new Error('Invalid or expired token')
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET'])
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify admin authentication
    const adminData = verifyAdminToken(req)

    // Get dashboard statistics
    const [
      ordersResult,
      productsResult,
      sidesResult,
      recentOrdersResult
    ] = await Promise.all([
      // Total orders and revenue
      supabase
        .from('orders')
        .select('id, total_amount, status, created_at')
        .order('created_at', { ascending: false }),
      
      // Products count
      supabase
        .from('products')
        .select('id, is_available')
        .eq('is_available', true),
      
      // Sides count
      supabase
        .from('sides')
        .select('id, is_available')
        .eq('is_available', true),
      
      // Recent orders with details
      supabase
        .from('orders')
        .select(`
          id,
          order_number,
          customer_email,
          total_amount,
          status,
          payment_status,
          created_at,
          order_items:order_items(
            product_name,
            side_name,
            quantity,
            total_price
          )
        `)
        .order('created_at', { ascending: false })
        .limit(10)
    ])

    if (ordersResult.error) throw ordersResult.error
    if (productsResult.error) throw productsResult.error
    if (sidesResult.error) throw sidesResult.error
    if (recentOrdersResult.error) throw recentOrdersResult.error

    const orders = ordersResult.data || []
    const products = productsResult.data || []
    const sides = sidesResult.data || []
    const recentOrders = recentOrdersResult.data || []

    // Calculate statistics
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const startOfWeek = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000))
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)

    const todayOrders = orders.filter(order => new Date(order.created_at) >= startOfDay)
    const weekOrders = orders.filter(order => new Date(order.created_at) >= startOfWeek)
    const monthOrders = orders.filter(order => new Date(order.created_at) >= startOfMonth)

    const totalRevenue = orders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0)
    const todayRevenue = todayOrders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0)
    const weekRevenue = weekOrders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0)
    const monthRevenue = monthOrders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0)

    // Order status breakdown
    const ordersByStatus = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1
      return acc
    }, {})

    // Popular items analysis
    const itemPopularity = {}
    recentOrders.forEach(order => {
      order.order_items?.forEach(item => {
        const itemName = item.product_name || item.side_name
        if (itemName) {
          itemPopularity[itemName] = (itemPopularity[itemName] || 0) + item.quantity
        }
      })
    })

    const popularItems = Object.entries(itemPopularity)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([name, quantity]) => ({ name, quantity }))

    // Prepare dashboard data
    const dashboardData = {
      overview: {
        totalOrders: orders.length,
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        activeProducts: products.length,
        activeSides: sides.length,
        averageOrderValue: orders.length > 0 ? Math.round((totalRevenue / orders.length) * 100) / 100 : 0
      },
      
      timeBasedStats: {
        today: {
          orders: todayOrders.length,
          revenue: Math.round(todayRevenue * 100) / 100
        },
        week: {
          orders: weekOrders.length,
          revenue: Math.round(weekRevenue * 100) / 100
        },
        month: {
          orders: monthOrders.length,
          revenue: Math.round(monthRevenue * 100) / 100
        }
      },
      
      orderStatus: ordersByStatus,
      
      popularItems,
      
      recentOrders: recentOrders.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        customerEmail: order.customer_email,
        totalAmount: parseFloat(order.total_amount),
        status: order.status,
        paymentStatus: order.payment_status,
        itemCount: order.order_items?.length || 0,
        createdAt: order.created_at
      })),
      
      adminInfo: {
        adminId: adminData.adminId,
        email: adminData.email,
        role: adminData.role
      }
    }

    res.status(200).json({
      success: true,
      data: dashboardData
    })

  } catch (error) {
    console.error('Admin dashboard error:', error)
    
    if (error.message.includes('token') || error.message.includes('authorization')) {
      res.status(401).json({
        error: 'Unauthorized',
        message: error.message
      })
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      })
    }
  }
}
